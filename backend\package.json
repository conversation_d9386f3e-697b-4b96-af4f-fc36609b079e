{"name": "ticket-migration-backend", "version": "1.0.0", "description": "Backend API for ticket migration tool", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "axios": "^1.5.0", "sqlite3": "^5.1.6", "socket.io": "^4.7.2", "multer": "^1.4.5-lts.1", "form-data": "^4.0.0", "winston": "^3.10.0", "node-cron": "^3.0.2", "uuid": "^9.0.0", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3"}}