import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error);
    
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      console.error(`HTTP ${status}:`, data);
    } else if (error.request) {
      // Request was made but no response received
      console.error('No response received:', error.request);
    } else {
      // Something else happened
      console.error('Request setup error:', error.message);
    }
    
    return Promise.reject(error);
  }
);

// Migration API endpoints
export const migrationApi = {
  // Start a new migration
  start: (config) => api.post('/migration/start', config),
  
  // Get migration status
  getStatus: (migrationId) => api.get(`/migration/status/${migrationId}`),
  
  // Get all migrations
  getList: (params = {}) => api.get('/migration/list', { params }),
  
  // Stop/cancel migration
  stop: (migrationId) => api.post(`/migration/stop/${migrationId}`),
};

// Configuration API endpoints
export const configApi = {
  // Get configuration
  get: () => api.get('/config'),
  
  // Update configuration
  update: (config) => api.post('/config', { config }),
  
  // Test API connection
  testConnection: (connectionData) => api.post('/config/test-connection', connectionData),
  
  // Get API information
  getApiInfo: (type) => api.get(`/config/api-info/${type}`),
};

// Reports API endpoints
export const reportsApi = {
  // Get migration report
  getReport: (migrationId) => api.get(`/reports/${migrationId}`),
  
  // Get all reports
  getReports: (params = {}) => api.get('/reports', { params }),
  
  // Export report as CSV
  exportReport: (migrationId) => api.get(`/reports/${migrationId}/export`, {
    responseType: 'blob'
  }),
};

// Health check
export const healthApi = {
  check: () => api.get('/health'),
};

// Helper function to handle API errors
export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    return {
      status,
      message: data.message || data.error || 'An error occurred',
      details: data.details || null
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      status: 0,
      message: 'Network error - please check your connection',
      details: null
    };
  } else {
    // Something else happened
    return {
      status: 0,
      message: error.message || 'An unexpected error occurred',
      details: null
    };
  }
};

export default api;
