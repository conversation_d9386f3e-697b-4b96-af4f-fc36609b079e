const express = require('express');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const { getDatabase } = require('../database/init');
const MigrationService = require('../services/migrationService');
const { getSocketInstance } = require('../services/socketService');

const router = express.Router();

// Start a new migration
router.post('/start', async (req, res) => {
  try {
    const { sourceUrl, sourceApiKey, targetUrl, targetApiKey, options = {} } = req.body;
    
    // Validate required fields
    if (!sourceUrl || !sourceApiKey || !targetUrl || !targetApiKey) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['sourceUrl', 'sourceApiKey', 'targetUrl', 'targetApiKey']
      });
    }

    const migrationId = uuidv4();
    const db = getDatabase();
    
    // Insert migration record
    await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO migrations (id, source_url, source_api_key, target_url, target_api_key, status)
         VALUES (?, ?, ?, ?, ?, 'initializing')`,
        [migrationId, sourceUrl, sourceApiKey, targetUrl, targetApiKey],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    // Start migration process
    const migrationService = new MigrationService({
      migrationId,
      sourceUrl,
      sourceApiKey,
      targetUrl,
      targetApiKey,
      options
    });

    // Start migration in background
    migrationService.start().catch(error => {
      logger.error('Migration failed:', error);
    });

    logger.logMigrationStart(migrationId, { sourceUrl, targetUrl });

    res.json({
      success: true,
      migrationId,
      message: 'Migration started successfully'
    });

  } catch (error) {
    logger.error('Error starting migration:', error);
    res.status(500).json({
      error: 'Failed to start migration',
      message: error.message
    });
  }
});

// Get migration status
router.get('/status/:migrationId', async (req, res) => {
  try {
    const { migrationId } = req.params;
    const db = getDatabase();

    const migration = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM migrations WHERE id = ?',
        [migrationId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!migration) {
      return res.status(404).json({
        error: 'Migration not found'
      });
    }

    // Get detailed ticket status
    const ticketStats = await new Promise((resolve, reject) => {
      db.all(
        `SELECT status, COUNT(*) as count 
         FROM ticket_migrations 
         WHERE migration_id = ? 
         GROUP BY status`,
        [migrationId],
        (err, rows) => {
          if (err) reject(err);
          else {
            const stats = {};
            rows.forEach(row => {
              stats[row.status] = row.count;
            });
            resolve(stats);
          }
        }
      );
    });

    res.json({
      migration,
      ticketStats,
      progress: migration.total_tickets > 0 ? 
        (migration.processed_tickets / migration.total_tickets) * 100 : 0
    });

  } catch (error) {
    logger.error('Error getting migration status:', error);
    res.status(500).json({
      error: 'Failed to get migration status',
      message: error.message
    });
  }
});

// Get all migrations
router.get('/list', async (req, res) => {
  try {
    const db = getDatabase();
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const migrations = await new Promise((resolve, reject) => {
      db.all(
        `SELECT id, source_url, target_url, status, total_tickets, 
                processed_tickets, successful_tickets, failed_tickets,
                created_at, started_at, completed_at
         FROM migrations 
         ORDER BY created_at DESC 
         LIMIT ? OFFSET ?`,
        [limit, offset],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    const total = await new Promise((resolve, reject) => {
      db.get(
        'SELECT COUNT(*) as count FROM migrations',
        (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        }
      );
    });

    res.json({
      migrations,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    logger.error('Error getting migrations list:', error);
    res.status(500).json({
      error: 'Failed to get migrations list',
      message: error.message
    });
  }
});

// Stop/cancel a migration
router.post('/stop/:migrationId', async (req, res) => {
  try {
    const { migrationId } = req.params;
    const db = getDatabase();

    // Update migration status to cancelled
    await new Promise((resolve, reject) => {
      db.run(
        'UPDATE migrations SET status = ?, completed_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['cancelled', migrationId],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    // Emit cancellation event
    const io = getSocketInstance();
    if (io) {
      io.emit('migrationCancelled', { migrationId });
    }

    logger.info('Migration cancelled', { migrationId });

    res.json({
      success: true,
      message: 'Migration cancelled successfully'
    });

  } catch (error) {
    logger.error('Error cancelling migration:', error);
    res.status(500).json({
      error: 'Failed to cancel migration',
      message: error.message
    });
  }
});

module.exports = router;
