import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Tabs,
  Tab,
  Paper
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  PlayArrow as MigrationIcon,
  Assessment as ReportsIcon
} from '@mui/icons-material';

const Navigation = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const getTabValue = () => {
    const path = location.pathname;
    if (path === '/') return 0;
    if (path.startsWith('/configuration')) return 1;
    if (path.startsWith('/migration')) return 2;
    if (path.startsWith('/reports')) return 3;
    return 0;
  };

  const handleTabChange = (event, newValue) => {
    const routes = ['/', '/configuration', '/migration', '/reports'];
    navigate(routes[newValue]);
  };

  return (
    <Paper elevation={1} sx={{ borderRadius: 0 }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={getTabValue()}
          onChange={handleTabChange}
          aria-label="navigation tabs"
          variant="fullWidth"
          sx={{
            '& .MuiTab-root': {
              minHeight: 64,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500,
            }
          }}
        >
          <Tab
            icon={<DashboardIcon />}
            label="Dashboard"
            iconPosition="start"
          />
          <Tab
            icon={<SettingsIcon />}
            label="Configuration"
            iconPosition="start"
          />
          <Tab
            icon={<MigrationIcon />}
            label="Migration"
            iconPosition="start"
          />
          <Tab
            icon={<ReportsIcon />}
            label="Reports"
            iconPosition="start"
          />
        </Tabs>
      </Box>
    </Paper>
  );
};

export default Navigation;
