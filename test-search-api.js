const axios = require('axios');

// Test the correct search API format
async function testSearchAPI() {
  console.log('🔍 Testing PandoHelp Search API with correct format...\n');
  
  const api = axios.create({
    baseURL: 'https://pandohelp.freshdesk.com/api/v2',
    auth: {
      username: 'KcYLr6Q1V4zoDtnl1sKs',
      password: 'X'
    },
    timeout: 15000
  });
  
  // Valid ticket types from the API response
  const validTypes = [
    'CAH', 'PWC-SAKS Global', 'IPG', 'Inspire Brands', 'Costa Forms', 
    '<PERSON><PERSON>', '<PERSON>', 'HACH', 'Uline', 'Accuride'
  ];
  
  try {
    // Test 1: Simple search with one ticket type
    console.log('📡 Testing search with CAH type...');
    const searchQuery = `type:'CAH' AND created_at:>'2020-01-01' AND created_at:<'2020-12-31'`;
    const encodedQuery = encodeURIComponent(`"${searchQuery}"`);
    
    console.log('Query:', searchQuery);
    console.log('Encoded:', encodedQuery);
    
    const response = await api.get(`/search/tickets?query=${encodedQuery}`);
    console.log(`✅ Search API: ${response.status} - Found ${response.data.results?.length || 0} tickets`);
    
    if (response.data.results && response.data.results.length > 0) {
      console.log('📋 Sample tickets:');
      response.data.results.slice(0, 3).forEach((ticket, index) => {
        console.log(`  ${index + 1}. ID: ${ticket.id}, Subject: ${ticket.subject}, Type: ${ticket.type}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Search API Error:', error.response?.status, error.response?.statusText);
    if (error.response?.data) {
      console.error('Error details:', JSON.stringify(error.response.data, null, 2));
    }
  }
  
  try {
    // Test 2: Regular tickets API as fallback
    console.log('\n📡 Testing regular tickets API...');
    const response = await api.get('/tickets?per_page=10');
    console.log(`✅ Regular API: ${response.status} - Found ${response.data.length} tickets`);
    
    if (response.data.length > 0) {
      console.log('📋 Available ticket types in recent tickets:');
      const types = [...new Set(response.data.map(t => t.type).filter(Boolean))];
      types.forEach(type => {
        const isValid = validTypes.includes(type);
        console.log(`  ${isValid ? '✅' : '❌'} ${type}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Regular API Error:', error.response?.status, error.response?.statusText);
  }
  
  console.log('\n🎯 Valid ticket types for migration:');
  validTypes.forEach(type => console.log(`  - ${type}`));
}

testSearchAPI().catch(console.error);
