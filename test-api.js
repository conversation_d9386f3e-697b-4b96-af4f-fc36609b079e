const axios = require('axios');

// Test API connections
async function testAPIs() {
  console.log('🔍 Testing API Connections...\n');
  
  // Source API (Kambaa)
  const sourceApi = axios.create({
    baseURL: 'https://kambaa.freshdesk.com/api/v2',
    auth: {
      username: 'LgcukXyOv4B7sAzRQcI',
      password: 'X'
    },
    timeout: 10000
  });
  
  // Target API (Kambaa1726)
  const targetApi = axios.create({
    baseURL: 'https://kambaa1726.freshdesk.com/api/v2',
    auth: {
      username: 'HxYmKx74LzI7sQALHmQu',
      password: 'X'
    },
    timeout: 10000
  });
  
  try {
    console.log('📡 Testing Source API (kambaa.freshdesk.com)...');
    const sourceResponse = await sourceApi.get('/tickets?per_page=5');
    console.log(`✅ Source API: ${sourceResponse.status} - Found ${sourceResponse.data.length} tickets`);
    
    if (sourceResponse.data.length > 0) {
      console.log('📋 Sample tickets from source:');
      sourceResponse.data.forEach((ticket, index) => {
        console.log(`  ${index + 1}. ID: ${ticket.id}, Subject: ${ticket.subject}, Type: ${ticket.type || 'N/A'}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Source API Error:', error.response?.status, error.response?.statusText || error.message);
  }
  
  try {
    console.log('\n📡 Testing Target API (kambaa1726.freshdesk.com)...');
    const targetResponse = await targetApi.get('/tickets?per_page=5');
    console.log(`✅ Target API: ${targetResponse.status} - Found ${targetResponse.data.length} tickets`);
    
  } catch (error) {
    console.error('❌ Target API Error:', error.response?.status, error.response?.statusText || error.message);
  }
  
  // Test with date filters
  try {
    console.log('\n📅 Testing Source API with date filters...');
    const dateFilterResponse = await sourceApi.get('/tickets', {
      params: {
        'created_at[gte]': '2020-01-01',
        'created_at[lte]': '2020-01-31',
        per_page: 10
      }
    });
    console.log(`✅ Date Filter Test: Found ${dateFilterResponse.data.length} tickets for Jan 2020`);
    
  } catch (error) {
    console.error('❌ Date Filter Error:', error.response?.status, error.response?.statusText || error.message);
  }
  
  // Test ticket types
  try {
    console.log('\n🏷️ Testing ticket types in source...');
    const allTicketsResponse = await sourceApi.get('/tickets?per_page=50');
    const tickets = allTicketsResponse.data;
    
    console.log(`📊 Analyzing ${tickets.length} tickets for types:`);
    const typeCount = {};
    const targetTypes = ['CAH', 'PWC-SAKS Global', 'IPG', 'Inspire Brands', 'Costa Forms', 'Robertshaw', 'Albert Heijn', 'HACH', 'Uline', 'Accuride'];
    
    tickets.forEach(ticket => {
      const type = ticket.type || ticket.cf_ticket_type || ticket.custom_fields?.cf_ticket_type || 'Unknown';
      typeCount[type] = (typeCount[type] || 0) + 1;
    });
    
    console.log('📈 Ticket types found:');
    Object.entries(typeCount).forEach(([type, count]) => {
      const isTarget = targetTypes.some(targetType => 
        type.toLowerCase().includes(targetType.toLowerCase()) || 
        targetType.toLowerCase().includes(type.toLowerCase())
      );
      console.log(`  ${isTarget ? '✅' : '❌'} ${type}: ${count} tickets`);
    });
    
  } catch (error) {
    console.error('❌ Ticket Type Analysis Error:', error.response?.status, error.response?.statusText || error.message);
  }
}

testAPIs().catch(console.error);
