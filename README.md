# Ticket Migration Tool

A comprehensive ticket migration tool for transferring tickets between support systems like Freshdesk. This tool provides a Node.js backend with Express API and a React frontend for managing ticket migrations.

## Features

- **Real-time Migration**: Background processing with live progress updates
- **Comprehensive Transfer**: Migrates tickets, conversations, and attachments
- **Error Handling**: Robust error recovery and detailed failure reporting
- **Rate Limiting**: Respects API rate limits to prevent throttling
- **Progress Tracking**: Real-time status updates and migration reports
- **Persistent State**: Migration continues even after frontend refresh
- **Detailed Logging**: Comprehensive logs for debugging and monitoring

## Architecture

```
ticket-migration-tool/
├── backend/                 # Node.js Express API
│   ├── server.js           # Main server file
│   ├── routes/             # API routes
│   ├── services/           # Business logic
│   ├── models/             # Database models
│   ├── utils/              # Utility functions
│   └── database/           # SQLite database
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
└── docs/                   # Documentation
```

## Quick Start

1. **Install dependencies**:
   ```bash
   npm run install:all
   ```

2. **Configure environment**:
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your API credentials
   ```

3. **Start development servers**:
   ```bash
   npm run dev
   ```

4. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## Configuration

Set up your source and target API credentials in the backend/.env file:

```env
SOURCE_API_URL=https://source.freshdesk.com/api/v2
SOURCE_API_KEY=your_source_api_key
TARGET_API_URL=https://target.freshdesk.com/api/v2
TARGET_API_KEY=your_target_api_key
```

## Usage

1. Configure source and target API credentials
2. Start the migration process
3. Monitor real-time progress in the dashboard
4. Review migration reports for success/failure details

## API Endpoints

- `POST /api/migration/start` - Start ticket migration
- `GET /api/migration/status` - Get migration status
- `GET /api/migration/reports` - Get migration reports
- `POST /api/config` - Update API configuration

## License

MIT License
