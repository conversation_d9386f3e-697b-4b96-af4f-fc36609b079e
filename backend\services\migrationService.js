const axios = require('axios');
const logger = require('../utils/logger');
const { getDatabase } = require('../database/init');
const { 
  emitMigrationProgress, 
  emitMigrationStatus, 
  emitTicketUpdate, 
  emitError 
} = require('./socketService');

class MigrationService {
  constructor(config) {
    this.migrationId = config.migrationId;
    this.sourceUrl = config.sourceUrl;
    this.sourceApiKey = config.sourceApiKey;
    this.targetUrl = config.targetUrl;
    this.targetApiKey = config.targetApiKey;
    this.options = config.options || {};
    
    this.batchSize = this.options.batchSize || parseInt(process.env.BATCH_SIZE) || 50;
    this.maxRetries = this.options.maxRetries || parseInt(process.env.MAX_RETRIES) || 3;
    this.retryDelay = this.options.retryDelay || parseInt(process.env.RETRY_DELAY_MS) || 5000;
    this.rateLimit = this.options.rateLimit || parseInt(process.env.RATE_LIMIT_DELAY_MS) || 1000;
    
    this.isRunning = false;
    this.shouldStop = false;
    
    // Setup axios instances with response/request interceptors for logging
    this.sourceApi = axios.create({
      baseURL: this.sourceUrl,
      auth: {
        username: this.sourceApiKey,
        password: 'X'
      },
      timeout: 30000
    });

    this.targetApi = axios.create({
      baseURL: this.targetUrl,
      auth: {
        username: this.targetApiKey,
        password: 'X'
      },
      timeout: 30000
    });

    // Add request/response interceptors for detailed API logging
    this.setupApiLogging();
  }

  setupApiLogging() {
    // Source API logging
    this.sourceApi.interceptors.request.use(
      (config) => {
        logger.info('Source API Request', {
          migrationId: this.migrationId,
          method: config.method?.toUpperCase(),
          url: config.url,
          params: config.params,
          timestamp: new Date().toISOString()
        });
        return config;
      },
      (error) => {
        logger.error('Source API Request Error', {
          migrationId: this.migrationId,
          error: error.message,
          timestamp: new Date().toISOString()
        });
        return Promise.reject(error);
      }
    );

    this.sourceApi.interceptors.response.use(
      (response) => {
        const filteredResponse = this.filterApiResponse(response, 'source');
        logger.info('Source API Response', {
          migrationId: this.migrationId,
          method: response.config.method?.toUpperCase(),
          url: response.config.url,
          status: response.status,
          statusText: response.statusText,
          dataSize: JSON.stringify(response.data).length,
          filteredData: filteredResponse,
          timestamp: new Date().toISOString()
        });
        return response;
      },
      (error) => {
        logger.error('Source API Response Error', {
          migrationId: this.migrationId,
          method: error.config?.method?.toUpperCase(),
          url: error.config?.url,
          status: error.response?.status,
          statusText: error.response?.statusText,
          message: error.message,
          responseData: error.response?.data,
          timestamp: new Date().toISOString()
        });
        return Promise.reject(error);
      }
    );

    // Target API logging
    this.targetApi.interceptors.request.use(
      (config) => {
        logger.info('Target API Request', {
          migrationId: this.migrationId,
          method: config.method?.toUpperCase(),
          url: config.url,
          dataSize: config.data ? JSON.stringify(config.data).length : 0,
          timestamp: new Date().toISOString()
        });
        return config;
      },
      (error) => {
        logger.error('Target API Request Error', {
          migrationId: this.migrationId,
          error: error.message,
          timestamp: new Date().toISOString()
        });
        return Promise.reject(error);
      }
    );

    this.targetApi.interceptors.response.use(
      (response) => {
        const filteredResponse = this.filterApiResponse(response, 'target');
        logger.info('Target API Response', {
          migrationId: this.migrationId,
          method: response.config.method?.toUpperCase(),
          url: response.config.url,
          status: response.status,
          statusText: response.statusText,
          dataSize: JSON.stringify(response.data).length,
          filteredData: filteredResponse,
          timestamp: new Date().toISOString()
        });
        return response;
      },
      (error) => {
        logger.error('Target API Response Error', {
          migrationId: this.migrationId,
          method: error.config?.method?.toUpperCase(),
          url: error.config?.url,
          status: error.response?.status,
          statusText: error.response?.statusText,
          message: error.message,
          responseData: error.response?.data,
          timestamp: new Date().toISOString()
        });
        return Promise.reject(error);
      }
    );
  }

  filterApiResponse(response, apiType) {
    try {
      const data = response.data;
      const url = response.config.url;

      // Filter response based on endpoint and API type
      if (url.includes('/tickets')) {
        return this.filterTicketsResponse(data, apiType);
      } else if (url.includes('/contacts')) {
        return this.filterContactsResponse(data, apiType);
      } else if (url.includes('/companies')) {
        return this.filterCompaniesResponse(data, apiType);
      } else if (url.includes('/conversations')) {
        return this.filterConversationsResponse(data, apiType);
      } else if (url.includes('/search')) {
        return this.filterSearchResponse(data, apiType);
      }

      // Default filtering for unknown endpoints
      return this.filterGenericResponse(data, apiType);
    } catch (error) {
      logger.warn('Error filtering API response', {
        migrationId: this.migrationId,
        error: error.message,
        apiType
      });
      return { error: 'Failed to filter response', originalSize: JSON.stringify(response.data).length };
    }
  }

  filterTicketsResponse(data, apiType) {
    if (Array.isArray(data)) {
      return {
        count: data.length,
        sample: data.slice(0, 2).map(ticket => ({
          id: ticket.id,
          subject: ticket.subject?.substring(0, 50) + '...',
          status: ticket.status,
          priority: ticket.priority,
          created_at: ticket.created_at,
          requester_id: ticket.requester_id,
          company_id: ticket.company_id,
          type: ticket.type
        }))
      };
    } else if (data && data.id) {
      return {
        id: data.id,
        subject: data.subject?.substring(0, 50) + '...',
        status: data.status,
        priority: data.priority,
        created_at: data.created_at,
        requester_id: data.requester_id,
        company_id: data.company_id,
        type: data.type,
        description_length: data.description?.length || 0
      };
    }
    return { type: 'tickets', size: JSON.stringify(data).length };
  }

  filterContactsResponse(data, apiType) {
    if (Array.isArray(data)) {
      return {
        count: data.length,
        sample: data.slice(0, 2).map(contact => ({
          id: contact.id,
          name: contact.name,
          email: contact.email,
          company_id: contact.company_id,
          created_at: contact.created_at
        }))
      };
    } else if (data && data.id) {
      return {
        id: data.id,
        name: data.name,
        email: data.email,
        company_id: data.company_id,
        created_at: data.created_at
      };
    }
    return { type: 'contacts', size: JSON.stringify(data).length };
  }

  filterCompaniesResponse(data, apiType) {
    if (Array.isArray(data)) {
      return {
        count: data.length,
        sample: data.slice(0, 2).map(company => ({
          id: company.id,
          name: company.name,
          domains: company.domains,
          created_at: company.created_at
        }))
      };
    } else if (data && data.id) {
      return {
        id: data.id,
        name: data.name,
        domains: data.domains,
        created_at: data.created_at
      };
    }
    return { type: 'companies', size: JSON.stringify(data).length };
  }

  filterConversationsResponse(data, apiType) {
    if (Array.isArray(data)) {
      return {
        count: data.length,
        sample: data.slice(0, 2).map(conv => ({
          id: conv.id,
          user_id: conv.user_id,
          private: conv.private,
          body_length: conv.body?.length || 0,
          attachments_count: conv.attachments?.length || 0,
          created_at: conv.created_at
        }))
      };
    } else if (data && data.id) {
      return {
        id: data.id,
        user_id: data.user_id,
        private: data.private,
        body_length: data.body?.length || 0,
        attachments_count: data.attachments?.length || 0,
        created_at: data.created_at
      };
    }
    return { type: 'conversations', size: JSON.stringify(data).length };
  }

  filterSearchResponse(data, apiType) {
    return {
      total: data.total || 0,
      results_count: data.results?.length || 0,
      sample_results: data.results?.slice(0, 2).map(result => ({
        id: result.id,
        subject: result.subject?.substring(0, 50) + '...',
        status: result.status,
        created_at: result.created_at
      })) || []
    };
  }

  filterGenericResponse(data, apiType) {
    if (Array.isArray(data)) {
      return {
        type: 'array',
        count: data.length,
        sample_keys: data.length > 0 ? Object.keys(data[0]).slice(0, 5) : []
      };
    } else if (typeof data === 'object' && data !== null) {
      return {
        type: 'object',
        keys: Object.keys(data).slice(0, 10),
        size: JSON.stringify(data).length
      };
    }
    return { type: typeof data, value: data };
  }

  async start() {
    if (this.isRunning) {
      throw new Error('Migration is already running');
    }

    this.isRunning = true;
    this.shouldStop = false;

    try {
      await this.updateMigrationStatus('running', { started_at: new Date().toISOString() });
      emitMigrationStatus(this.migrationId, 'running');

      // Step 1: Call filter API and get the data
      logger.info('Step 1: Fetching tickets using ONLY filter API', { migrationId: this.migrationId });
      const tickets = await this.fetchAllTickets();

      await this.updateMigrationStatus('processing', {
        total_tickets: tickets.length
      });

      logger.info('Step 1 completed: Filter API returned tickets', {
        migrationId: this.migrationId,
        ticketCount: tickets.length
      });

      // Step 2: Process tickets in batches following the new workflow
      let processed = 0;
      let successful = 0;
      let failed = 0;

      for (let i = 0; i < tickets.length; i += this.batchSize) {
        if (this.shouldStop) {
          logger.info('Migration stopped by user', { migrationId: this.migrationId });
          break;
        }

        const batch = tickets.slice(i, i + this.batchSize);
        
        for (const ticket of batch) {
          if (this.shouldStop) break;

          try {
            await this.migrateTicket(ticket);
            successful++;
            emitTicketUpdate(this.migrationId, {
              ticketId: ticket.id,
              status: 'success'
            });
          } catch (error) {
            failed++;
            logger.logTicketMigration(this.migrationId, ticket.id, 'failed', error);
            emitTicketUpdate(this.migrationId, {
              ticketId: ticket.id,
              status: 'failed',
              error: error.message
            });
          }

          processed++;
          
          // Update progress
          await this.updateMigrationProgress(processed, successful, failed);
          emitMigrationProgress(this.migrationId, {
            processed,
            successful,
            failed,
            total: tickets.length,
            progress: (processed / tickets.length) * 100
          });

          // Rate limiting
          await this.delay(this.rateLimit);
        }
      }

      // Complete migration
      const finalStatus = this.shouldStop ? 'cancelled' : 'completed';
      await this.updateMigrationStatus(finalStatus, {
        completed_at: new Date().toISOString(),
        processed_tickets: processed,
        successful_tickets: successful,
        failed_tickets: failed
      });

      emitMigrationStatus(this.migrationId, finalStatus, {
        processed,
        successful,
        failed,
        total: tickets.length
      });

      logger.logMigrationComplete(this.migrationId, {
        total: tickets.length,
        processed,
        successful,
        failed
      });

    } catch (error) {
      logger.error('Migration failed:', error);
      await this.updateMigrationStatus('failed', {
        error_message: error.message,
        completed_at: new Date().toISOString()
      });
      emitError(this.migrationId, error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  async fetchAllTickets() {
    const tickets = [];

    // Get ticket types from options or use defaults
    const ticketTypes = this.options.ticketTypes || [
      'CAH',
      'PWC-SAKS Global',
      'IPG',
      'Inspire Brands',
      'Costa Forms',
      'Robertshaw',
      'Albert Heijn',
      'HACH',
      'Uline',
      'Accuride'
    ];

    logger.info('Using ONLY filter/search API for ticket fetching', {
      migrationId: this.migrationId,
      ticketTypes: ticketTypes.length
    });

    // Generate month ranges from configured dates or defaults
    const monthRanges = this.generateMonthRanges();

    logger.info(`Fetching tickets for ${monthRanges.length} month ranges with ${ticketTypes.length} ticket types using filter API`, {
      migrationId: this.migrationId
    });

    for (const monthRange of monthRanges) {
      if (this.shouldStop) break;

      try {
        const monthTickets = await this.fetchTicketsForMonthUsingFilterAPI(monthRange, ticketTypes);
        tickets.push(...monthTickets);

        logger.info(`Fetched ${monthTickets.length} tickets for ${monthRange.month} using filter API`, {
          migrationId: this.migrationId,
          month: monthRange.month,
          totalSoFar: tickets.length
        });

        // Rate limiting between month requests
        await this.delay(this.rateLimit);

      } catch (error) {
        logger.error(`Error fetching tickets for ${monthRange.month} using filter API:`, {
          migrationId: this.migrationId,
          month: monthRange.month,
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText
        });
        // Continue with next month instead of failing completely
      }
    }

    logger.info(`Total tickets fetched using filter API: ${tickets.length}`, {
      migrationId: this.migrationId
    });

    return tickets;
  }

  generateMonthRanges() {
    const ranges = [];
    const startDate = new Date(this.options.dateRangeFrom || '2020-01-01');
    const endDate = new Date(this.options.dateRangeTo || new Date().toISOString().split('T')[0]);

    let currentMonth = new Date(startDate);

    while (currentMonth <= endDate) {
      const year = currentMonth.getFullYear();
      const month = currentMonth.getMonth();

      // First day of the month
      const fromDate = new Date(year, month, 1);

      // Last day of the month
      const toDate = new Date(year, month + 1, 0, 23, 59, 59, 999);

      // Don't go beyond configured end date
      if (toDate > endDate) {
        toDate.setTime(endDate.getTime());
      }

      ranges.push({
        month: `${year}-${String(month + 1).padStart(2, '0')}`,
        from: fromDate.toISOString(),
        to: toDate.toISOString(),
        fromFormatted: fromDate.toISOString().split('T')[0],
        toFormatted: toDate.toISOString().split('T')[0]
      });

      // Move to next month
      currentMonth.setMonth(currentMonth.getMonth() + 1);
    }

    return ranges;
  }

  async fetchTicketsForMonthUsingFilterAPI(monthRange, ticketTypes) {
    const tickets = [];
    let page = 1;
    const maxPages = 10; // Freshdesk search API limit

    logger.info('Using filter API exclusively for ticket fetching', {
      migrationId: this.migrationId,
      month: monthRange.month,
      ticketTypes
    });

    while (page <= maxPages) {
      try {
        // Build search query using Freshdesk Search API format
        const query = this.buildSearchQuery(monthRange, ticketTypes);

        const params = {
          query: `"${query}"`,
          page: page
        };

        logger.info('Calling filter API', {
          migrationId: this.migrationId,
          month: monthRange.month,
          page,
          query,
          params
        });

        // Use ONLY the search/filter API endpoint
        const response = await this.sourceApi.get('/search/tickets', { params });
        const searchResults = response.data;

        // Log detailed search results
        logger.info('Filter API Response Details', {
          migrationId: this.migrationId,
          month: monthRange.month,
          page: page,
          query: query,
          total_results: searchResults.total || 0,
          results_returned: searchResults.results?.length || 0,
          response_size: JSON.stringify(searchResults).length
        });

        // Extract tickets from search results
        const pageTickets = searchResults.results || [];
        tickets.push(...pageTickets);

        // Check if there are more pages (Freshdesk returns 30 per page for search)
        const totalResults = searchResults.total || 0;
        const resultsPerPage = 30;
        const totalPages = Math.ceil(totalResults / resultsPerPage);

        const hasMore = page < totalPages && page < maxPages;
        page++;

        logger.info(`Filter API page ${page - 1} for ${monthRange.month}: ${pageTickets.length} tickets, total: ${totalResults}`, {
          migrationId: this.migrationId,
          query: query,
          hasMore
        });

        // Rate limiting between page requests
        await this.delay(this.rateLimit);

        // Break if no more results
        if (pageTickets.length === 0 || !hasMore) {
          break;
        }

      } catch (error) {
        logger.error(`Error with filter API page ${page} for ${monthRange.month}:`, {
          migrationId: this.migrationId,
          month: monthRange.month,
          page,
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          responseData: error.response?.data
        });

        // If filter API fails, throw error since we only use filter API
        throw new Error(`Filter API failed: ${error.message}`);
      }
    }

    logger.info(`Filter API completed for ${monthRange.month}: ${tickets.length} tickets`, {
      migrationId: this.migrationId,
      month: monthRange.month,
      totalTickets: tickets.length
    });

    return tickets;
  }

  async fetchTicketsForMonth(monthRange, ticketTypes) {
    const tickets = [];
    let page = 1;
    const maxPages = 10; // Freshdesk search API limit

    while (page <= maxPages) {
      try {
        // Build search query using Freshdesk Search API format
        const query = this.buildSearchQuery(monthRange, ticketTypes);

        const params = {
          query: `"${query}"`,
          page: page
        };

        // Use the search API endpoint for filtering
        const response = await this.sourceApi.get('/search/tickets', { params });
        const searchResults = response.data;

        // Log detailed search results
        logger.info('Search API Response Details', {
          migrationId: this.migrationId,
          month: monthRange.month,
          page: page,
          query: query,
          total_results: searchResults.total || 0,
          results_returned: searchResults.results?.length || 0,
          response_size: JSON.stringify(searchResults).length
        });

        // Extract tickets from search results
        const pageTickets = searchResults.results || [];
        tickets.push(...pageTickets);

        // Check if there are more pages (Freshdesk returns 30 per page for search)
        const totalResults = searchResults.total || 0;
        const resultsPerPage = 30;
        const totalPages = Math.ceil(totalResults / resultsPerPage);

        hasMore = page < totalPages && page < maxPages;
        page++;

        logger.debug(`Searched page ${page - 1} for ${monthRange.month}: ${pageTickets.length} tickets, total: ${totalResults}`, {
          migrationId: this.migrationId,
          query: query
        });

        // Rate limiting between page requests
        await this.delay(this.rateLimit);

        // Break if no more results
        if (pageTickets.length === 0) {
          break;
        }

      } catch (error) {
        logger.error(`Error searching page ${page} for ${monthRange.month}:`, {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText
        });

        // If search API fails, fall back to regular tickets API
        if (error.response && error.response.status === 400) {
          logger.warn('Search API failed, falling back to regular tickets API');
          return await this.fetchTicketsForMonthFallback(monthRange, ticketTypes);
        }

        // If it's a rate limit error, wait longer and retry
        if (error.response && error.response.status === 429) {
          logger.warn('Rate limit hit, waiting 60 seconds before retry');
          await this.delay(60000);
          continue; // Retry the same page
        }

        throw error;
      }
    }

    return tickets;
  }

  buildSearchQuery(monthRange, ticketTypes) {
    const queryParts = [];

    // Add date range filter
    const fromDate = monthRange.fromFormatted; // YYYY-MM-DD format
    const toDate = monthRange.toFormatted;

    queryParts.push(`created_at:>${fromDate}`);
    queryParts.push(`created_at:<${toDate}`);

    // Add ticket type filter if specified
    if (ticketTypes && ticketTypes.length > 0) {
      const typeQueries = ticketTypes.map(type => `type:'${type}'`);
      queryParts.push(`(${typeQueries.join(' OR ')})`);
    }

    // Combine with AND
    return queryParts.join(' AND ');
  }

  async fetchTicketsForMonthFallback(monthRange, ticketTypes) {
    // Fallback to regular tickets API with client-side filtering
    const tickets = [];
    let page = 1;
    let hasMore = true;

    while (hasMore && page <= 10) { // Limit pages to avoid infinite loops
      try {
        const params = {
          page,
          per_page: 100
        };

        const response = await this.sourceApi.get('/tickets', { params });
        const pageTickets = response.data;

        // Filter tickets by date range and type on client side
        const filteredTickets = this.filterTickets(pageTickets, monthRange, ticketTypes);
        tickets.push(...filteredTickets);

        // Check if there are more pages
        hasMore = pageTickets.length === 100;
        page++;

        logger.debug(`Fallback: Fetched page ${page - 1} for ${monthRange.month}: ${pageTickets.length} tickets, ${filteredTickets.length} after filtering`, {
          migrationId: this.migrationId
        });

        // Rate limiting between page requests
        await this.delay(this.rateLimit);

      } catch (error) {
        logger.error(`Error in fallback fetch for ${monthRange.month}:`, {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText
        });
        break; // Exit loop on error
      }
    }

    return tickets;
  }

  filterTickets(tickets, monthRange, ticketTypes) {
    return tickets.filter(ticket => {
      // Filter by date range
      const createdAt = new Date(ticket.created_at);
      const fromDate = new Date(monthRange.from);
      const toDate = new Date(monthRange.to);

      const isInDateRange = createdAt >= fromDate && createdAt <= toDate;
      if (!isInDateRange) return false;

      // Filter by ticket types if specified
      if (!ticketTypes || ticketTypes.length === 0) {
        return true; // No type filter, include all tickets in date range
      }

      // Check various possible fields where ticket type might be stored
      const ticketType = ticket.type ||
                        ticket.cf_ticket_type ||
                        ticket.custom_fields?.cf_ticket_type ||
                        ticket.custom_fields?.ticket_type ||
                        ticket.subject; // Sometimes type is in subject

      if (!ticketType) return false;

      // Check if ticket type matches any of the required types
      return ticketTypes.some(type => {
        if (typeof ticketType === 'string') {
          return ticketType.toLowerCase().includes(type.toLowerCase()) ||
                 type.toLowerCase().includes(ticketType.toLowerCase());
        }
        return false;
      });
    });
  }

  async migrateTicket(sourceTicket) {
    const db = getDatabase();

    logger.info('Starting ticket migration workflow', {
      migrationId: this.migrationId,
      sourceTicketId: sourceTicket.id,
      subject: sourceTicket.subject?.substring(0, 50) + '...'
    });

    // Insert ticket migration record
    const ticketMigrationId = await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO ticket_migrations (migration_id, source_ticket_id, status)
         VALUES (?, ?, 'processing')`,
        [this.migrationId, sourceTicket.id],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });

    try {
      // Step 2: Create/Get Contact and Company, associate them
      logger.info('Step 2: Processing contact and company from filter data', {
        migrationId: this.migrationId,
        sourceTicketId: sourceTicket.id,
        requester_id: sourceTicket.requester_id,
        company_id: sourceTicket.company_id
      });

      let targetRequesterId = null;
      let targetCompanyId = null;

      // Handle contact creation/association
      if (sourceTicket.requester_id) {
        targetRequesterId = await this.createOrGetContact(sourceTicket.requester_id);
      }

      // Handle company creation/association
      if (sourceTicket.company_id) {
        targetCompanyId = await this.createOrGetCompany(sourceTicket.company_id);
      }

      // Associate contact with company if both exist
      if (targetRequesterId && targetCompanyId) {
        await this.associateContactWithCompany(targetRequesterId, targetCompanyId);
      }

      // Step 3: Get values from Filter and create ticket in target instance
      logger.info('Step 3: Creating ticket in target instance using filter data', {
        migrationId: this.migrationId,
        sourceTicketId: sourceTicket.id,
        targetRequesterId,
        targetCompanyId
      });

      const targetTicketData = await this.transformTicketDataFromFilter(sourceTicket, targetRequesterId, targetCompanyId);

      const targetTicketResponse = await this.targetApi.post('/tickets', targetTicketData);
      const targetTicket = targetTicketResponse.data;

      logger.info('Step 3 completed: Ticket created successfully in target system', {
        migrationId: this.migrationId,
        sourceTicketId: sourceTicket.id,
        targetTicketId: targetTicket.id,
        targetTicketSubject: targetTicket.subject?.substring(0, 50) + '...'
      });

      // Update ticket migration record
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE ticket_migrations
           SET target_ticket_id = ?, status = 'success', migrated_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [targetTicket.id, ticketMigrationId],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Step 4: Create conversations for the ticket
      logger.info('Step 4: Creating conversations for the ticket', {
        migrationId: this.migrationId,
        sourceTicketId: sourceTicket.id,
        targetTicketId: targetTicket.id
      });

      await this.migrateConversations(sourceTicket.id, targetTicket.id, ticketMigrationId);

      logger.info('Step 4 completed: All conversations migrated', {
        migrationId: this.migrationId,
        sourceTicketId: sourceTicket.id,
        targetTicketId: targetTicket.id
      });

      logger.logTicketMigration(this.migrationId, sourceTicket.id, 'success');

    } catch (error) {
      logger.error('Ticket migration workflow failed', {
        migrationId: this.migrationId,
        sourceTicketId: sourceTicket.id,
        error: error.message,
        stack: error.stack
      });

      // Update ticket migration record with error
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE ticket_migrations
           SET status = 'failed', error_message = ?
           WHERE id = ?`,
          [error.message, ticketMigrationId],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      throw error;
    }
  }

  async createOrGetContact(sourceContactId) {
    const db = getDatabase();

    logger.info('Creating or getting contact', {
      migrationId: this.migrationId,
      sourceContactId
    });

    // Check if contact already exists in migration records
    const existingContact = await new Promise((resolve, reject) => {
      db.get(
        'SELECT target_contact_id FROM contact_migrations WHERE source_contact_id = ? AND status = "success"',
        [sourceContactId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingContact) {
      logger.info('Contact already exists, skipping creation', {
        migrationId: this.migrationId,
        sourceContactId,
        targetContactId: existingContact.target_contact_id
      });
      return existingContact.target_contact_id;
    }

    try {
      // Fetch contact from source using regular API (not filter API)
      logger.info('Fetching contact details from source', {
        migrationId: this.migrationId,
        sourceContactId
      });

      const sourceContactResponse = await this.sourceApi.get(`/contacts/${sourceContactId}`);
      const sourceContact = sourceContactResponse.data;

      // Check if contact already exists in target by email
      const existingTargetContact = await this.findExistingContactByEmail(sourceContact.email);
      if (existingTargetContact) {
        logger.info('Contact already exists in target system, using existing', {
          migrationId: this.migrationId,
          sourceContactId,
          targetContactId: existingTargetContact.id,
          email: sourceContact.email
        });

        // Record the mapping
        await this.recordContactMigration(sourceContactId, existingTargetContact.id, sourceContact, 'existing');
        return existingTargetContact.id;
      }

      // Transform and create new contact
      const targetContactData = this.transformContactData(sourceContact);

      logger.info('Creating new contact in target system', {
        migrationId: this.migrationId,
        sourceContactId,
        contactEmail: sourceContact.email,
        contactName: sourceContact.name
      });

      const targetContactResponse = await this.targetApi.post('/contacts', targetContactData);
      const targetContact = targetContactResponse.data;

      // Record contact migration
      await this.recordContactMigration(sourceContactId, targetContact.id, sourceContact, 'success');

      logger.info('Contact created successfully', {
        migrationId: this.migrationId,
        sourceContactId,
        targetContactId: targetContact.id,
        email: sourceContact.email
      });

      return targetContact.id;

    } catch (error) {
      logger.error('Failed to create or get contact', {
        migrationId: this.migrationId,
        sourceContactId,
        error: error.message,
        status: error.response?.status
      });

      // Record failed contact migration
      await this.recordContactMigration(sourceContactId, null, null, 'failed', error.message);
      throw error;
    }
  }

  async createOrGetCompany(sourceCompanyId) {
    const db = getDatabase();

    logger.info('Creating or getting company', {
      migrationId: this.migrationId,
      sourceCompanyId
    });

    // Check if company already exists in migration records
    const existingCompany = await new Promise((resolve, reject) => {
      db.get(
        'SELECT target_company_id FROM company_migrations WHERE source_company_id = ? AND status = "success"',
        [sourceCompanyId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingCompany) {
      logger.info('Company already exists, skipping creation', {
        migrationId: this.migrationId,
        sourceCompanyId,
        targetCompanyId: existingCompany.target_company_id
      });
      return existingCompany.target_company_id;
    }

    try {
      // Fetch company from source using regular API (not filter API)
      logger.info('Fetching company details from source', {
        migrationId: this.migrationId,
        sourceCompanyId
      });

      const sourceCompanyResponse = await this.sourceApi.get(`/companies/${sourceCompanyId}`);
      const sourceCompany = sourceCompanyResponse.data;

      // Check if company already exists in target by name or domain
      const existingTargetCompany = await this.findExistingCompanyByNameOrDomain(sourceCompany.name, sourceCompany.domains);
      if (existingTargetCompany) {
        logger.info('Company already exists in target system, using existing', {
          migrationId: this.migrationId,
          sourceCompanyId,
          targetCompanyId: existingTargetCompany.id,
          name: sourceCompany.name
        });

        // Record the mapping
        await this.recordCompanyMigration(sourceCompanyId, existingTargetCompany.id, sourceCompany, 'existing');
        return existingTargetCompany.id;
      }

      // Transform and create new company
      const targetCompanyData = this.transformCompanyData(sourceCompany);

      logger.info('Creating new company in target system', {
        migrationId: this.migrationId,
        sourceCompanyId,
        companyName: sourceCompany.name,
        companyDomains: sourceCompany.domains
      });

      const targetCompanyResponse = await this.targetApi.post('/companies', targetCompanyData);
      const targetCompany = targetCompanyResponse.data;

      // Record company migration
      await this.recordCompanyMigration(sourceCompanyId, targetCompany.id, sourceCompany, 'success');

      logger.info('Company created successfully', {
        migrationId: this.migrationId,
        sourceCompanyId,
        targetCompanyId: targetCompany.id,
        name: sourceCompany.name
      });

      return targetCompany.id;

    } catch (error) {
      logger.error('Failed to create or get company', {
        migrationId: this.migrationId,
        sourceCompanyId,
        error: error.message,
        status: error.response?.status
      });

      // Record failed company migration
      await this.recordCompanyMigration(sourceCompanyId, null, null, 'failed', error.message);
      throw error;
    }
  }

  async associateContactWithCompany(targetContactId, targetCompanyId) {
    try {
      logger.info('Associating contact with company', {
        migrationId: this.migrationId,
        targetContactId,
        targetCompanyId
      });

      // Update contact to associate with company
      await this.targetApi.put(`/contacts/${targetContactId}`, {
        company_id: targetCompanyId
      });

      logger.info('Contact associated with company successfully', {
        migrationId: this.migrationId,
        targetContactId,
        targetCompanyId
      });

    } catch (error) {
      logger.error('Failed to associate contact with company', {
        migrationId: this.migrationId,
        targetContactId,
        targetCompanyId,
        error: error.message,
        status: error.response?.status
      });
      // Don't throw - this is not critical
    }
  }

  async transformTicketDataFromFilter(sourceTicket, targetRequesterId, targetCompanyId) {
    logger.info('Transforming ticket data from filter results', {
      migrationId: this.migrationId,
      sourceTicketId: sourceTicket.id,
      targetRequesterId,
      targetCompanyId
    });

    // Transform source ticket data to target format using filter data
    return {
      subject: sourceTicket.subject,
      description: sourceTicket.description || 'Migrated ticket from filter API',
      priority: sourceTicket.priority,
      status: sourceTicket.status,
      requester_id: targetRequesterId,
      company_id: targetCompanyId,
      type: sourceTicket.type,
      source: 1, // Email source
      tags: sourceTicket.tags || [],
      custom_fields: sourceTicket.custom_fields || {},
      // Preserve original ticket metadata from filter
      cc_emails: sourceTicket.cc_emails || [],
      due_by: sourceTicket.due_by,
      fr_due_by: sourceTicket.fr_due_by,
      group_id: sourceTicket.group_id,
      product_id: sourceTicket.product_id
    };
  }

  async transformTicketData(sourceTicket) {
    const db = getDatabase();

    // Get migrated contact ID
    let targetRequesterId = sourceTicket.requester_id;
    if (sourceTicket.requester_id) {
      const migratedContact = await new Promise((resolve, reject) => {
        db.get(
          'SELECT target_contact_id FROM contact_migrations WHERE source_contact_id = ? AND status = "success"',
          [sourceTicket.requester_id],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });
      if (migratedContact) {
        targetRequesterId = migratedContact.target_contact_id;
      }
    }

    // Get migrated company ID
    let targetCompanyId = null;
    if (sourceTicket.company_id) {
      const migratedCompany = await new Promise((resolve, reject) => {
        db.get(
          'SELECT target_company_id FROM company_migrations WHERE source_company_id = ? AND status = "success"',
          [sourceTicket.company_id],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });
      if (migratedCompany) {
        targetCompanyId = migratedCompany.target_company_id;
      }
    }

    // Transform source ticket data to target format
    return {
      subject: sourceTicket.subject,
      description: sourceTicket.description || 'Migrated ticket',
      priority: sourceTicket.priority,
      status: sourceTicket.status,
      requester_id: targetRequesterId,
      company_id: targetCompanyId,
      type: sourceTicket.type,
      source: 1, // Email source
      tags: sourceTicket.tags || [],
      custom_fields: sourceTicket.custom_fields || {},
      // Preserve original ticket metadata
      cc_emails: sourceTicket.cc_emails || [],
      due_by: sourceTicket.due_by,
      fr_due_by: sourceTicket.fr_due_by,
      group_id: sourceTicket.group_id,
      product_id: sourceTicket.product_id
    };
  }

  async migrateConversations(sourceTicketId, targetTicketId, ticketMigrationId) {
    try {
      // Fetch conversations from source using Freshdesk API
      logger.info('Fetching conversations for ticket', {
        migrationId: this.migrationId,
        sourceTicketId,
        targetTicketId
      });

      const conversationsResponse = await this.sourceApi.get(`/tickets/${sourceTicketId}/conversations`);
      const conversations = conversationsResponse.data;

      logger.info('Conversations fetched, starting migration', {
        migrationId: this.migrationId,
        sourceTicketId,
        targetTicketId,
        conversationCount: conversations.length
      });

      for (const conversation of conversations) {
        await this.migrateConversation(conversation, targetTicketId, ticketMigrationId);
        await this.delay(this.rateLimit / 2); // Shorter delay for conversations
      }

      logger.info('All conversations migrated successfully', {
        migrationId: this.migrationId,
        sourceTicketId,
        targetTicketId,
        conversationCount: conversations.length
      });

    } catch (error) {
      logger.error('Error migrating conversations:', {
        migrationId: this.migrationId,
        sourceTicketId,
        targetTicketId,
        error: error.message,
        status: error.response?.status
      });
      // Don't throw - conversations are not critical
    }
  }

  async migrateConversation(sourceConversation, targetTicketId, ticketMigrationId) {
    try {
      logger.info('Migrating conversation', {
        migrationId: this.migrationId,
        conversationId: sourceConversation.id,
        targetTicketId,
        isPrivate: sourceConversation.private || false,
        hasAttachments: sourceConversation.attachments?.length > 0,
        bodyLength: sourceConversation.body?.length || 0
      });

      // Determine if this should be a reply or note based on conversation type
      const isPrivate = sourceConversation.private || false;
      const isFromAgent = sourceConversation.user_id && sourceConversation.user_id !== sourceConversation.ticket?.requester_id;

      let endpoint, conversationData;

      if (isPrivate || isFromAgent) {
        // Use notes endpoint for private conversations or agent responses
        endpoint = `/tickets/${targetTicketId}/notes`;
        conversationData = {
          body: sourceConversation.body,
          private: isPrivate,
          notify_emails: sourceConversation.notify_emails || []
        };
      } else {
        // Use reply endpoint for public customer responses
        endpoint = `/tickets/${targetTicketId}/reply`;
        conversationData = {
          body: sourceConversation.body
        };
      }

      logger.info('Conversation migration strategy determined', {
        migrationId: this.migrationId,
        conversationId: sourceConversation.id,
        endpoint,
        isPrivate,
        isFromAgent
      });

      // Handle attachments if present
      if (sourceConversation.attachments && sourceConversation.attachments.length > 0) {
        await this.migrateConversationWithAttachments(
          sourceConversation,
          targetTicketId,
          ticketMigrationId,
          endpoint,
          conversationData
        );
      } else {
        // Create conversation without attachments
        const targetConversationResponse = await this.targetApi.post(endpoint, conversationData);

        logger.info('Conversation migrated successfully', {
          migrationId: this.migrationId,
          sourceConversationId: sourceConversation.id,
          targetConversationId: targetConversationResponse.data.id,
          endpoint
        });

        // Record conversation migration
        await this.recordConversationMigration(
          ticketMigrationId,
          sourceConversation.id,
          targetConversationResponse.data.id,
          'success'
        );
      }

    } catch (error) {
      logger.error('Error migrating conversation:', {
        migrationId: this.migrationId,
        conversationId: sourceConversation.id,
        targetTicketId,
        error: error.message,
        status: error.response?.status
      });
      await this.recordConversationMigration(
        ticketMigrationId,
        sourceConversation.id,
        null,
        'failed',
        error.message
      );
    }
  }

  async migrateConversationWithAttachments(sourceConversation, targetTicketId, ticketMigrationId, endpoint, conversationData) {
    const FormData = require('form-data');
    const axios = require('axios');

    try {
      logger.info('Migrating conversation with attachments', {
        migrationId: this.migrationId,
        conversationId: sourceConversation.id,
        targetTicketId,
        attachmentCount: sourceConversation.attachments.length,
        endpoint
      });

      const form = new FormData();

      // Add conversation body
      form.append('body', conversationData.body);

      if (conversationData.private !== undefined) {
        form.append('private', conversationData.private);
      }

      if (conversationData.notify_emails && conversationData.notify_emails.length > 0) {
        conversationData.notify_emails.forEach(email => {
          form.append('notify_emails[]', email);
        });
      }

      // Download and attach files
      let attachedCount = 0;
      for (const attachment of sourceConversation.attachments) {
        try {
          logger.info('Downloading attachment', {
            migrationId: this.migrationId,
            conversationId: sourceConversation.id,
            attachmentId: attachment.id,
            attachmentName: attachment.name,
            attachmentSize: attachment.size
          });

          const attachmentResponse = await this.sourceApi.get(`/attachments/${attachment.id}`, {
            responseType: 'stream'
          });

          form.append('attachments[]', attachmentResponse.data, {
            filename: attachment.name,
            contentType: attachment.content_type
          });

          attachedCount++;
          logger.info('Attachment downloaded successfully', {
            migrationId: this.migrationId,
            conversationId: sourceConversation.id,
            attachmentId: attachment.id,
            attachmentName: attachment.name
          });

        } catch (attachmentError) {
          logger.error('Error downloading attachment:', {
            migrationId: this.migrationId,
            conversationId: sourceConversation.id,
            attachmentId: attachment.id,
            attachmentName: attachment.name,
            error: attachmentError.message,
            status: attachmentError.response?.status
          });
          // Continue with other attachments
        }
      }

      logger.info('Uploading conversation with attachments to target', {
        migrationId: this.migrationId,
        conversationId: sourceConversation.id,
        targetTicketId,
        attachedCount,
        totalAttachments: sourceConversation.attachments.length
      });

      // Send request with form data
      const response = await axios.post(
        `${this.targetUrl}${endpoint}`,
        form,
        {
          headers: {
            ...form.getHeaders(),
            'Authorization': `Basic ${Buffer.from(`${this.targetApiKey}:X`).toString('base64')}`
          },
          timeout: 60000 // Longer timeout for file uploads
        }
      );

      logger.info('Conversation with attachments migrated successfully', {
        migrationId: this.migrationId,
        sourceConversationId: sourceConversation.id,
        targetConversationId: response.data.id,
        attachedCount,
        endpoint
      });

      // Record successful conversation migration
      await this.recordConversationMigration(
        ticketMigrationId,
        sourceConversation.id,
        response.data.id,
        'success'
      );

    } catch (error) {
      logger.error('Error migrating conversation with attachments:', {
        migrationId: this.migrationId,
        conversationId: sourceConversation.id,
        targetTicketId,
        error: error.message,
        status: error.response?.status
      });
      await this.recordConversationMigration(
        ticketMigrationId,
        sourceConversation.id,
        null,
        'failed',
        error.message
      );
    }
  }

  async recordConversationMigration(ticketMigrationId, sourceConversationId, targetConversationId, status, errorMessage = null) {
    const db = getDatabase();

    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO conversation_migrations
                   (ticket_migration_id, source_conversation_id, target_conversation_id, status, error_message, migrated_at)
                   VALUES (?, ?, ?, ?, ?, ${status === 'success' ? 'CURRENT_TIMESTAMP' : 'NULL'})`;

      db.run(sql, [ticketMigrationId, sourceConversationId, targetConversationId, status, errorMessage], (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async updateMigrationStatus(status, additionalData = {}) {
    const db = getDatabase();
    const fields = Object.keys(additionalData);
    const values = Object.values(additionalData);
    
    let sql = 'UPDATE migrations SET status = ?';
    const params = [status];
    
    if (fields.length > 0) {
      sql += ', ' + fields.map(field => `${field} = ?`).join(', ');
      params.push(...values);
    }
    
    sql += ' WHERE id = ?';
    params.push(this.migrationId);

    return new Promise((resolve, reject) => {
      db.run(sql, params, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async updateMigrationProgress(processed, successful, failed) {
    return this.updateMigrationStatus('running', {
      processed_tickets: processed,
      successful_tickets: successful,
      failed_tickets: failed
    });
  }

  stop() {
    this.shouldStop = true;
    logger.info('Migration stop requested', { migrationId: this.migrationId });
  }

  async migrateContact(sourceContactId) {
    const db = getDatabase();

    // Check if contact already migrated
    const existingContact = await new Promise((resolve, reject) => {
      db.get(
        'SELECT target_contact_id FROM contact_migrations WHERE source_contact_id = ? AND status = "success"',
        [sourceContactId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingContact) {
      return existingContact.target_contact_id;
    }

    try {
      // Fetch contact from source
      logger.info('Fetching contact from source', {
        migrationId: this.migrationId,
        sourceContactId
      });

      const sourceContactResponse = await this.sourceApi.get(`/contacts/${sourceContactId}`);
      const sourceContact = sourceContactResponse.data;

      // Transform contact data for target system
      const targetContactData = this.transformContactData(sourceContact);

      logger.info('Creating contact in target system', {
        migrationId: this.migrationId,
        sourceContactId,
        contactEmail: sourceContact.email,
        contactName: sourceContact.name
      });

      // Create contact in target system
      const targetContactResponse = await this.targetApi.post('/contacts', targetContactData);
      const targetContact = targetContactResponse.data;

      // Record contact migration
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO contact_migrations
           (migration_id, source_contact_id, target_contact_id, email, name, status, migrated_at)
           VALUES (?, ?, ?, ?, ?, 'success', CURRENT_TIMESTAMP)`,
          [this.migrationId, sourceContactId, targetContact.id, sourceContact.email, sourceContact.name],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      logger.info('Contact migrated successfully', {
        migrationId: this.migrationId,
        sourceContactId,
        targetContactId: targetContact.id
      });

      return targetContact.id;

    } catch (error) {
      // Record failed contact migration
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO contact_migrations
           (migration_id, source_contact_id, status, error_message)
           VALUES (?, ?, 'failed', ?)`,
          [this.migrationId, sourceContactId, error.message],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      logger.error('Contact migration failed', {
        migrationId: this.migrationId,
        sourceContactId,
        error: error.message
      });

      throw error;
    }
  }

  async migrateCompany(sourceCompanyId) {
    const db = getDatabase();

    // Check if company already migrated
    const existingCompany = await new Promise((resolve, reject) => {
      db.get(
        'SELECT target_company_id FROM company_migrations WHERE source_company_id = ? AND status = "success"',
        [sourceCompanyId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingCompany) {
      return existingCompany.target_company_id;
    }

    try {
      // Fetch company from source
      logger.info('Fetching company from source', {
        migrationId: this.migrationId,
        sourceCompanyId
      });

      const sourceCompanyResponse = await this.sourceApi.get(`/companies/${sourceCompanyId}`);
      const sourceCompany = sourceCompanyResponse.data;

      // Transform company data for target system
      const targetCompanyData = this.transformCompanyData(sourceCompany);

      logger.info('Creating company in target system', {
        migrationId: this.migrationId,
        sourceCompanyId,
        companyName: sourceCompany.name,
        companyDomains: sourceCompany.domains
      });

      // Create company in target system
      const targetCompanyResponse = await this.targetApi.post('/companies', targetCompanyData);
      const targetCompany = targetCompanyResponse.data;

      // Record company migration
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO company_migrations
           (migration_id, source_company_id, target_company_id, name, domain, status, migrated_at)
           VALUES (?, ?, ?, ?, ?, 'success', CURRENT_TIMESTAMP)`,
          [this.migrationId, sourceCompanyId, targetCompany.id, sourceCompany.name, sourceCompany.domains?.[0]],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      logger.info('Company migrated successfully', {
        migrationId: this.migrationId,
        sourceCompanyId,
        targetCompanyId: targetCompany.id
      });

      return targetCompany.id;

    } catch (error) {
      // Record failed company migration
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO company_migrations
           (migration_id, source_company_id, status, error_message)
           VALUES (?, ?, 'failed', ?)`,
          [this.migrationId, sourceCompanyId, error.message],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      logger.error('Company migration failed', {
        migrationId: this.migrationId,
        sourceCompanyId,
        error: error.message
      });

      throw error;
    }
  }

  transformContactData(sourceContact) {
    return {
      name: sourceContact.name,
      email: sourceContact.email,
      phone: sourceContact.phone,
      mobile: sourceContact.mobile,
      twitter_id: sourceContact.twitter_id,
      unique_external_id: sourceContact.unique_external_id,
      other_emails: sourceContact.other_emails || [],
      company_id: null, // Will be set after company migration
      view_all_tickets: sourceContact.view_all_tickets || false,
      other_companies: sourceContact.other_companies || [],
      address: sourceContact.address,
      avatar: sourceContact.avatar,
      description: sourceContact.description,
      job_title: sourceContact.job_title,
      language: sourceContact.language || 'en',
      tags: sourceContact.tags || [],
      time_zone: sourceContact.time_zone || 'Eastern Time (US & Canada)',
      custom_fields: sourceContact.custom_fields || {}
    };
  }

  transformCompanyData(sourceCompany) {
    return {
      name: sourceCompany.name,
      description: sourceCompany.description,
      note: sourceCompany.note,
      domains: sourceCompany.domains || [],
      health_score: sourceCompany.health_score,
      account_tier: sourceCompany.account_tier,
      renewal_date: sourceCompany.renewal_date,
      industry: sourceCompany.industry,
      custom_fields: sourceCompany.custom_fields || {}
    };
  }

  async testSearchAPI() {
    try {
      // Test if search API is available with a simple query
      const response = await this.sourceApi.get('/search/tickets', {
        params: { query: '"priority:1"' }
      });

      logger.info('Search API is available', {
        migrationId: this.migrationId,
        testResponseStatus: response.status,
        testResponseSize: JSON.stringify(response.data).length
      });
      return true;
    } catch (error) {
      logger.warn('Search API not available, will use regular API', {
        migrationId: this.migrationId,
        error: error.message,
        status: error.response?.status
      });
      return false;
    }
  }

  async fetchAllTicketsWithRegularAPI(ticketTypes) {
    const tickets = [];
    let page = 1;
    let hasMore = true;
    const maxPages = 50; // Reasonable limit to avoid infinite loops

    logger.info('Using regular API with client-side filtering', {
      migrationId: this.migrationId,
      ticketTypes: ticketTypes.length
    });

    while (hasMore && page <= maxPages) {
      try {
        const params = {
          page,
          per_page: 100,
          order_by: 'created_at',
          order_type: 'asc'
        };

        const response = await this.sourceApi.get('/tickets', { params });
        const pageTickets = response.data;

        if (pageTickets.length === 0) {
          break; // No more tickets
        }

        // Apply date and type filtering
        const dateFrom = new Date(this.options.dateRangeFrom || '2020-01-01');
        const dateTo = new Date(this.options.dateRangeTo || new Date().toISOString().split('T')[0]);

        const filteredTickets = pageTickets.filter(ticket => {
          // Date filter
          const createdAt = new Date(ticket.created_at);
          const isInDateRange = createdAt >= dateFrom && createdAt <= dateTo;

          if (!isInDateRange) return false;

          // Type filter
          if (!ticketTypes || ticketTypes.length === 0) return true;

          const ticketType = ticket.type || ticket.subject || '';
          return ticketTypes.some(type =>
            ticketType.toLowerCase().includes(type.toLowerCase()) ||
            type.toLowerCase().includes(ticketType.toLowerCase())
          );
        });

        tickets.push(...filteredTickets);

        logger.info(`Fetched page ${page}: ${pageTickets.length} tickets, ${filteredTickets.length} after filtering`, {
          migrationId: this.migrationId,
          totalFiltered: tickets.length
        });

        // Check if there are more pages
        hasMore = pageTickets.length === 100;
        page++;

        // Rate limiting
        await this.delay(this.rateLimit);

      } catch (error) {
        logger.error('Error fetching tickets with regular API:', {
          migrationId: this.migrationId,
          page,
          message: error.message,
          status: error.response?.status
        });
        break; // Exit on error
      }
    }

    logger.info(`Total tickets fetched with regular API: ${tickets.length}`, {
      migrationId: this.migrationId
    });

    return tickets;
  }

  async findExistingContactByEmail(email) {
    try {
      logger.info('Checking if contact exists in target by email', {
        migrationId: this.migrationId,
        email
      });

      const response = await this.targetApi.get('/contacts', {
        params: { email: email }
      });

      const contacts = response.data;
      if (contacts && contacts.length > 0) {
        logger.info('Found existing contact in target', {
          migrationId: this.migrationId,
          email,
          targetContactId: contacts[0].id
        });
        return contacts[0];
      }

      return null;
    } catch (error) {
      logger.warn('Error checking for existing contact', {
        migrationId: this.migrationId,
        email,
        error: error.message
      });
      return null;
    }
  }

  async findExistingCompanyByNameOrDomain(name, domains) {
    try {
      logger.info('Checking if company exists in target by name or domain', {
        migrationId: this.migrationId,
        name,
        domains
      });

      // Try to find by name first
      let response = await this.targetApi.get('/companies', {
        params: { name: name }
      });

      let companies = response.data;
      if (companies && companies.length > 0) {
        logger.info('Found existing company in target by name', {
          migrationId: this.migrationId,
          name,
          targetCompanyId: companies[0].id
        });
        return companies[0];
      }

      // Try to find by domain if available
      if (domains && domains.length > 0) {
        for (const domain of domains) {
          try {
            response = await this.targetApi.get('/companies', {
              params: { domain: domain }
            });

            companies = response.data;
            if (companies && companies.length > 0) {
              logger.info('Found existing company in target by domain', {
                migrationId: this.migrationId,
                domain,
                targetCompanyId: companies[0].id
              });
              return companies[0];
            }
          } catch (domainError) {
            // Continue with next domain
          }
        }
      }

      return null;
    } catch (error) {
      logger.warn('Error checking for existing company', {
        migrationId: this.migrationId,
        name,
        domains,
        error: error.message
      });
      return null;
    }
  }

  async recordContactMigration(sourceContactId, targetContactId, sourceContact, status, errorMessage = null) {
    const db = getDatabase();

    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO contact_migrations
                   (migration_id, source_contact_id, target_contact_id, email, name, status, error_message, migrated_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ${status === 'success' || status === 'existing' ? 'CURRENT_TIMESTAMP' : 'NULL'})`;

      const email = sourceContact?.email || null;
      const name = sourceContact?.name || null;

      db.run(sql, [this.migrationId, sourceContactId, targetContactId, email, name, status, errorMessage], (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async recordCompanyMigration(sourceCompanyId, targetCompanyId, sourceCompany, status, errorMessage = null) {
    const db = getDatabase();

    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO company_migrations
                   (migration_id, source_company_id, target_company_id, name, domain, status, error_message, migrated_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ${status === 'success' || status === 'existing' ? 'CURRENT_TIMESTAMP' : 'NULL'})`;

      const name = sourceCompany?.name || null;
      const domain = sourceCompany?.domains?.[0] || null;

      db.run(sql, [this.migrationId, sourceCompanyId, targetCompanyId, name, domain, status, errorMessage], (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = MigrationService;
