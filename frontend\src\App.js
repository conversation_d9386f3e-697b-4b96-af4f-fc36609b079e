import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Container, AppBar, Toolbar, Typography, Box } from '@mui/material';
import { styled } from '@mui/material/styles';

import Dashboard from './pages/Dashboard';
import Configuration from './pages/Configuration';
import Migration from './pages/Migration';
import Reports from './pages/Reports';
import Navigation from './components/Navigation';
import { SocketProvider } from './contexts/SocketContext';

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
}));

const MainContainer = styled(Container)(({ theme }) => ({
  marginTop: theme.spacing(3),
  marginBottom: theme.spacing(3),
}));

function App() {
  return (
    <SocketProvider>
      <Box sx={{ flexGrow: 1 }}>
        <StyledAppBar position="static">
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              Ticket Migration Tool
            </Typography>
          </Toolbar>
        </StyledAppBar>
        
        <Navigation />
        
        <MainContainer maxWidth="xl">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/configuration" element={<Configuration />} />
            <Route path="/migration" element={<Migration />} />
            <Route path="/migration/:migrationId" element={<Migration />} />
            <Route path="/reports" element={<Reports />} />
            <Route path="/reports/:migrationId" element={<Reports />} />
          </Routes>
        </MainContainer>
      </Box>
    </SocketProvider>
  );
}

export default App;
