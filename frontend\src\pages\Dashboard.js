import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Box,
  Button,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Assessment as ReportsIcon,
  Settings as ConfigIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Schedule as PendingIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';

import { migrationApi, reportsApi, handleApiError } from '../services/api';
import { useSocket } from '../contexts/SocketContext';

const Dashboard = () => {
  const navigate = useNavigate();
  const { connected, migrationProgress, migrationStatus } = useSocket();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [recentMigrations, setRecentMigrations] = useState([]);
  const [overallStats, setOverallStats] = useState({
    total_migrations: 0,
    total_tickets: 0,
    total_successful: 0,
    total_failed: 0,
    successRate: 0
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load recent migrations
      const migrationsResponse = await migrationApi.getList({ limit: 5 });
      setRecentMigrations(migrationsResponse.data.migrations);

      // Load overall statistics
      const reportsResponse = await reportsApi.getReports();
      setOverallStats(reportsResponse.data.overallStats);

    } catch (err) {
      const errorInfo = handleApiError(err);
      setError(errorInfo.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'running': case 'processing': return 'primary';
      case 'failed': return 'error';
      case 'cancelled': return 'warning';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <SuccessIcon />;
      case 'failed': return <ErrorIcon />;
      case 'running': case 'processing': return <PendingIcon />;
      default: return <PendingIcon />;
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return 'N/A';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Migration Dashboard
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {!connected && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          Not connected to migration server. Real-time updates may not work.
        </Alert>
      )}

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Migrations
              </Typography>
              <Typography variant="h4">
                {overallStats.total_migrations || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Tickets
              </Typography>
              <Typography variant="h4">
                {overallStats.total_tickets || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Success Rate
              </Typography>
              <Typography variant="h4" color="success.main">
                {overallStats.successRate || 0}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Failed Tickets
              </Typography>
              <Typography variant="h4" color="error.main">
                {overallStats.total_failed || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
          <Box display="flex" gap={2} flexWrap="wrap">
            <Button
              variant="contained"
              startIcon={<StartIcon />}
              onClick={() => navigate('/migration')}
              size="large"
            >
              Start New Migration
            </Button>
            <Button
              variant="outlined"
              startIcon={<ConfigIcon />}
              onClick={() => navigate('/configuration')}
              size="large"
            >
              Configure APIs
            </Button>
            <Button
              variant="outlined"
              startIcon={<ReportsIcon />}
              onClick={() => navigate('/reports')}
              size="large"
            >
              View Reports
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Recent Migrations */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Migrations
          </Typography>
          
          {recentMigrations.length === 0 ? (
            <Typography color="textSecondary">
              No migrations found. Start your first migration to see it here.
            </Typography>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Status</TableCell>
                    <TableCell>Source</TableCell>
                    <TableCell>Target</TableCell>
                    <TableCell>Progress</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {recentMigrations.map((migration) => {
                    const progress = migrationProgress[migration.id];
                    const status = migrationStatus[migration.id]?.status || migration.status;
                    const progressPercent = progress?.progress || 
                      (migration.total_tickets > 0 ? 
                        (migration.processed_tickets / migration.total_tickets) * 100 : 0);

                    return (
                      <TableRow key={migration.id}>
                        <TableCell>
                          <Chip
                            icon={getStatusIcon(status)}
                            label={status}
                            color={getStatusColor(status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new URL(migration.source_url).hostname}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new URL(migration.target_url).hostname}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ minWidth: 120 }}>
                            <LinearProgress
                              variant="determinate"
                              value={progressPercent}
                              sx={{ mb: 1 }}
                            />
                            <Typography variant="caption">
                              {migration.processed_tickets || 0} / {migration.total_tickets || 0} tickets
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {format(new Date(migration.created_at), 'MMM dd, yyyy HH:mm')}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Button
                            size="small"
                            onClick={() => navigate(`/migration/${migration.id}`)}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default Dashboard;
