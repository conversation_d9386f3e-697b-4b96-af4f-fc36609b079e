const logger = require('../utils/logger');

let io = null;

const initializeSocket = (socketIo) => {
  io = socketIo;
  
  io.on('connection', (socket) => {
    logger.info('Client connected', { socketId: socket.id });
    
    // Join migration room for specific migration updates
    socket.on('joinMigration', (migrationId) => {
      socket.join(`migration_${migrationId}`);
      logger.info('Client joined migration room', { 
        socketId: socket.id, 
        migrationId 
      });
    });
    
    // Leave migration room
    socket.on('leaveMigration', (migrationId) => {
      socket.leave(`migration_${migrationId}`);
      logger.info('Client left migration room', { 
        socketId: socket.id, 
        migrationId 
      });
    });
    
    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info('Client disconnected', { socketId: socket.id });
    });
    
    // Send initial connection confirmation
    socket.emit('connected', { 
      message: 'Connected to migration server',
      timestamp: new Date().toISOString()
    });
  });
  
  logger.info('Socket.IO initialized');
};

const getSocketInstance = () => {
  return io;
};

// Emit migration progress update
const emitMigrationProgress = (migrationId, data) => {
  if (io) {
    io.to(`migration_${migrationId}`).emit('migrationProgress', {
      migrationId,
      ...data,
      timestamp: new Date().toISOString()
    });
  }
};

// Emit migration status change
const emitMigrationStatus = (migrationId, status, data = {}) => {
  if (io) {
    io.to(`migration_${migrationId}`).emit('migrationStatus', {
      migrationId,
      status,
      ...data,
      timestamp: new Date().toISOString()
    });
  }
};

// Emit ticket migration update
const emitTicketUpdate = (migrationId, ticketData) => {
  if (io) {
    io.to(`migration_${migrationId}`).emit('ticketUpdate', {
      migrationId,
      ...ticketData,
      timestamp: new Date().toISOString()
    });
  }
};

// Emit error notification
const emitError = (migrationId, error) => {
  if (io) {
    io.to(`migration_${migrationId}`).emit('migrationError', {
      migrationId,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

// Emit general notification to all clients
const emitNotification = (type, message, data = {}) => {
  if (io) {
    io.emit('notification', {
      type,
      message,
      ...data,
      timestamp: new Date().toISOString()
    });
  }
};

module.exports = {
  initializeSocket,
  getSocketInstance,
  emitMigrationProgress,
  emitMigrationStatus,
  emitTicketUpdate,
  emitError,
  emitNotification
};
