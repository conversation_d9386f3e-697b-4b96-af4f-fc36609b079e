const axios = require('axios');

async function compareQueryFormats() {
  console.log('🔍 Comparing Query Formats with Your Working Curl Example...\n');
  
  const api = axios.create({
    baseURL: 'https://pandohelp.freshdesk.com/api/v2',
    auth: {
      username: 'KcYLr6Q1V4zoDtnl1sKs',
      password: 'X'
    },
    timeout: 15000
  });
  
  // Your working curl query (decoded)
  const yourWorkingQuery = `"(type:'Question' OR type:'Problem') AND (due_by:>'2017-10-01' AND due_by:<'2017-10-07')"`;
  
  // Current implementation format
  const currentImplQuery = `"created_at:>2020-01-01 AND created_at:<2020-12-31 AND (type:'CAH' OR type:'PWC-SAKS Global')"`;
  
  // Test format matching your structure but with valid types
  const matchingFormatQuery = `"(type:'CAH' OR type:'PWC-SAKS Global') AND (created_at:>'2020-01-01' AND created_at:<'2020-12-31')"`;
  
  console.log('📋 Query Formats to Test:');
  console.log('1. Your working format:', yourWorkingQuery);
  console.log('2. Current implementation:', currentImplQuery);
  console.log('3. Matching your structure:', matchingFormatQuery);
  console.log('');
  
  // Test 1: Your working query (should fail due to invalid types)
  try {
    console.log('📡 Test 1: Your working query format...');
    const response = await api.get('/search/tickets', { params: { query: yourWorkingQuery } });
    console.log(`✅ SUCCESS: ${response.status} - Found ${response.data.results?.length || 0} tickets`);
  } catch (error) {
    console.log(`❌ FAILED: ${error.response?.status} ${error.response?.statusText}`);
    if (error.response?.data?.errors) {
      console.log('   Error details:', JSON.stringify(error.response.data.errors, null, 2));
    }
  }
  
  // Test 2: Current implementation format
  try {
    console.log('\n📡 Test 2: Current implementation format...');
    const response = await api.get('/search/tickets', { params: { query: currentImplQuery } });
    console.log(`✅ SUCCESS: ${response.status} - Found ${response.data.results?.length || 0} tickets`);
  } catch (error) {
    console.log(`❌ FAILED: ${error.response?.status} ${error.response?.statusText}`);
    if (error.response?.data?.errors) {
      console.log('   Error details:', JSON.stringify(error.response.data.errors, null, 2));
    }
  }
  
  // Test 3: Matching your structure with valid types
  try {
    console.log('\n📡 Test 3: Matching your structure with valid types...');
    const response = await api.get('/search/tickets', { params: { query: matchingFormatQuery } });
    console.log(`✅ SUCCESS: ${response.status} - Found ${response.data.results?.length || 0} tickets`);
    
    if (response.data.results && response.data.results.length > 0) {
      console.log('📋 Sample results:');
      response.data.results.slice(0, 2).forEach((ticket, i) => {
        console.log(`  ${i + 1}. ID: ${ticket.id}, Type: ${ticket.type}, Created: ${ticket.created_at}`);
      });
    }
  } catch (error) {
    console.log(`❌ FAILED: ${error.response?.status} ${error.response?.statusText}`);
    if (error.response?.data?.errors) {
      console.log('   Error details:', JSON.stringify(error.response.data.errors, null, 2));
    }
  }
  
  // Test 4: Simple working query to verify API access
  try {
    console.log('\n📡 Test 4: Simple working query...');
    const simpleQuery = `"type:'CAH'"`;
    const response = await api.get('/search/tickets', { params: { query: simpleQuery } });
    console.log(`✅ SUCCESS: ${response.status} - Found ${response.data.results?.length || 0} tickets`);
  } catch (error) {
    console.log(`❌ FAILED: ${error.response?.status} ${error.response?.statusText}`);
  }
  
  console.log('\n🎯 Conclusion:');
  console.log('The current implementation should work if:');
  console.log('1. Valid ticket types are used (CAH, PWC-SAKS Global, etc.)');
  console.log('2. Date format is correct (YYYY-MM-DD)');
  console.log('3. Query structure matches Freshdesk requirements');
}

compareQueryFormats().catch(console.error);
