const axios = require('axios');

async function simpleAPITest() {
  console.log('🔍 Simple API Test for pandohelp.freshdesk.com...\n');
  
  const api = axios.create({
    baseURL: 'https://pandohelp.freshdesk.com/api/v2',
    auth: {
      username: 'KcYLr6Q1V4zoDtnl1sKs',
      password: 'X'
    },
    timeout: 15000
  });

  // Test 1: Basic connection
  try {
    console.log('📡 Test 1: Basic API connection...');
    const response = await api.get('/tickets?per_page=5');
    console.log(`✅ SUCCESS: ${response.status} - Found ${response.data.length} tickets`);
    
    if (response.data.length > 0) {
      const ticket = response.data[0];
      console.log('📋 Sample ticket structure:');
      console.log({
        id: ticket.id,
        subject: ticket.subject?.substring(0, 50) + '...',
        type: ticket.type,
        created_at: ticket.created_at,
        status: ticket.status,
        priority: ticket.priority
      });
      
      // Check custom fields
      if (ticket.custom_fields) {
        console.log('🔧 Custom fields available:', Object.keys(ticket.custom_fields));
      }
    }
  } catch (error) {
    console.error('❌ FAILED: Basic API connection');
    console.error(`   Status: ${error.response?.status}`);
    console.error(`   Message: ${error.response?.statusText}`);
    console.error(`   Details: ${JSON.stringify(error.response?.data)}`);
    return; // Exit if basic connection fails
  }

  // Test 2: Check if search endpoint exists
  try {
    console.log('\n📡 Test 2: Testing search endpoint availability...');
    const response = await api.get('/search/tickets?query="priority:1"');
    console.log(`✅ SUCCESS: Search endpoint works - ${response.status}`);
    console.log(`   Results: ${response.data.results?.length || 0} tickets`);
    console.log(`   Total: ${response.data.total || 0} tickets`);
  } catch (error) {
    console.error('❌ FAILED: Search endpoint');
    console.error(`   Status: ${error.response?.status}`);
    console.error(`   Message: ${error.response?.statusText}`);
    console.error(`   Details: ${JSON.stringify(error.response?.data)}`);
    
    // If search fails, let's try alternative approaches
    console.log('\n🔄 Trying alternative search methods...');
    
    // Try without quotes
    try {
      const response2 = await api.get('/search/tickets?query=priority:1');
      console.log(`✅ Alternative 1 SUCCESS: ${response2.status}`);
    } catch (err2) {
      console.error(`❌ Alternative 1 FAILED: ${err2.response?.status}`);
    }
    
    // Try different endpoint
    try {
      const response3 = await api.get('/tickets/filter?query=priority:1');
      console.log(`✅ Alternative 2 SUCCESS: ${response3.status}`);
    } catch (err3) {
      console.error(`❌ Alternative 2 FAILED: ${err3.response?.status}`);
    }
  }

  // Test 3: Check available ticket fields
  try {
    console.log('\n📡 Test 3: Analyzing available ticket data...');
    const response = await api.get('/tickets?per_page=20');
    const tickets = response.data;
    
    // Analyze ticket types
    const types = new Set();
    const subjects = [];
    const customFieldKeys = new Set();
    
    tickets.forEach(ticket => {
      if (ticket.type) types.add(ticket.type);
      if (ticket.subject) subjects.push(ticket.subject);
      if (ticket.custom_fields) {
        Object.keys(ticket.custom_fields).forEach(key => customFieldKeys.add(key));
      }
    });
    
    console.log(`📊 Analysis of ${tickets.length} tickets:`);
    console.log(`   Types found: ${Array.from(types).join(', ') || 'None'}`);
    console.log(`   Custom fields: ${Array.from(customFieldKeys).join(', ') || 'None'}`);
    
    // Check for target types in subjects
    const targetTypes = ['CAH', 'PWC-SAKS Global', 'IPG', 'Inspire Brands', 'Costa Forms', 'Robertshaw', 'Albert Heijn', 'HACH', 'Uline', 'Accuride'];
    let foundInSubjects = 0;
    let foundInTypes = 0;
    
    tickets.forEach(ticket => {
      const subject = ticket.subject || '';
      const type = ticket.type || '';
      
      targetTypes.forEach(targetType => {
        if (subject.toLowerCase().includes(targetType.toLowerCase())) {
          foundInSubjects++;
        }
        if (type.toLowerCase().includes(targetType.toLowerCase())) {
          foundInTypes++;
        }
      });
    });
    
    console.log(`🎯 Target type analysis:`);
    console.log(`   Found in subjects: ${foundInSubjects} tickets`);
    console.log(`   Found in types: ${foundInTypes} tickets`);
    
    if (foundInSubjects > 0 || foundInTypes > 0) {
      console.log('✅ Target ticket types are present in your data!');
    } else {
      console.log('⚠️  Target ticket types not found in sample data');
    }
    
  } catch (error) {
    console.error('❌ FAILED: Ticket analysis');
    console.error(`   Status: ${error.response?.status}`);
    console.error(`   Message: ${error.response?.statusText}`);
  }

  // Test 4: Test date-based filtering with regular API
  try {
    console.log('\n📡 Test 4: Testing date filtering with regular API...');
    
    // Try different date parameter formats
    const dateTests = [
      { name: 'updated_since', params: { updated_since: '2024-01-01T00:00:00Z', per_page: 5 } },
      { name: 'created_since', params: { created_since: '2024-01-01T00:00:00Z', per_page: 5 } }
    ];
    
    for (const test of dateTests) {
      try {
        const response = await api.get('/tickets', { params: test.params });
        console.log(`   ✅ ${test.name}: Found ${response.data.length} tickets`);
      } catch (err) {
        console.log(`   ❌ ${test.name}: ${err.response?.status} ${err.response?.statusText}`);
      }
    }
    
  } catch (error) {
    console.error('❌ FAILED: Date filtering test');
  }

  console.log('\n🎯 SUMMARY:');
  console.log('If basic API works but search fails, we can use:');
  console.log('1. Regular /tickets endpoint with pagination');
  console.log('2. Client-side filtering by date and type');
  console.log('3. Process all tickets and filter locally');
}

simpleAPITest().catch(console.error);
