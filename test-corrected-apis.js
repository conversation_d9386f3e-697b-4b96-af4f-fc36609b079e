const axios = require('axios');

// Test API connections with corrected configuration
async function testCorrectedAPIs() {
  console.log('🔍 Testing Corrected API Connections...\n');
  
  // Source API (PandoHelp)
  const sourceApi = axios.create({
    baseURL: 'https://pandohelp.freshdesk.com/api/v2',
    auth: {
      username: 'KcYLr6Q1V4zoDtnl1sKs',
      password: 'X'
    },
    timeout: 10000
  });
  
  // Target API (Kambaa1726)
  const targetApi = axios.create({
    baseURL: 'https://kambaa1726.freshdesk.com/api/v2',
    auth: {
      username: 'HxYmKx74LzI7sQALHmQu',
      password: 'X'
    },
    timeout: 10000
  });
  
  try {
    console.log('📡 Testing Source API (pandohelp.freshdesk.com)...');
    const sourceResponse = await sourceApi.get('/tickets?per_page=5');
    console.log(`✅ Source API: ${sourceResponse.status} - Found ${sourceResponse.data.length} tickets`);
    
    if (sourceResponse.data.length > 0) {
      console.log('📋 Sample tickets from source:');
      sourceResponse.data.forEach((ticket, index) => {
        console.log(`  ${index + 1}. ID: ${ticket.id}, Subject: ${ticket.subject}, Status: ${ticket.status}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Source API Error:', error.response?.status, error.response?.statusText || error.message);
  }
  
  try {
    console.log('\n📡 Testing Target API (kambaa1726.freshdesk.com)...');
    const targetResponse = await targetApi.get('/tickets?per_page=5');
    console.log(`✅ Target API: ${targetResponse.status} - Found ${targetResponse.data.length} tickets`);
    
    if (targetResponse.data.length > 0) {
      console.log('📋 Sample tickets from target:');
      targetResponse.data.forEach((ticket, index) => {
        console.log(`  ${index + 1}. ID: ${ticket.id}, Subject: ${ticket.subject}, Status: ${ticket.status}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Target API Error:', error.response?.status, error.response?.statusText || error.message);
  }
  
  console.log('\n🎯 Configuration Summary:');
  console.log('Source (FROM): pandohelp.freshdesk.com');
  console.log('Target (TO): kambaa1726.freshdesk.com');
  console.log('✅ Ready for migration!');
}

testCorrectedAPIs().catch(console.error);
