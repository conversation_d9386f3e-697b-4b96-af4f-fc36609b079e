# Ticket Migration Tool - Setup Guide

This guide will help you set up and run the Ticket Migration Tool for migrating tickets between Freshdesk instances.

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager
- Source and target Freshdesk API credentials

## Installation

### 1. Clone or Download the Project

```bash
git clone <repository-url>
cd tictetmigration
```

### 2. Install Dependencies

Install all dependencies for both backend and frontend:

```bash
npm run install:all
```

Or install manually:

```bash
# Install root dependencies
npm install

# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

### 3. Configure Environment Variables

Copy the example environment file and configure your settings:

```bash
cd backend
cp .env.example .env
```

Edit `backend/.env` with your configuration:

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_PATH=./database/migration.db

# Source API Configuration (Your current Freshdesk)
SOURCE_API_URL=https://kambaa.freshdesk.com/api/v2
SOURCE_API_KEY=LgcukXyOv4B7sAzRQcI
SOURCE_API_TYPE=freshdesk

# Target API Configuration (Destination Freshdesk)
TARGET_API_URL=https://your-target-domain.freshdesk.com/api/v2
TARGET_API_KEY=your_target_api_key_here
TARGET_API_TYPE=freshdesk

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_DELAY_MS=1000

# Migration Configuration
BATCH_SIZE=50
MAX_RETRIES=3
RETRY_DELAY_MS=5000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/migration.log

# Security
JWT_SECRET=your_jwt_secret_here
BCRYPT_ROUNDS=10

# WebSocket Configuration
SOCKET_CORS_ORIGIN=http://localhost:3000
```

### 4. API Key Setup

#### For Freshdesk:

1. **Get your API Key:**
   - Log in to your Freshdesk account
   - Go to Profile Settings → API Key
   - Copy your API key

2. **API URL Format:**
   - Source: `https://your-source-domain.freshdesk.com/api/v2`
   - Target: `https://your-target-domain.freshdesk.com/api/v2`

3. **Authentication:**
   - Username: Your API Key
   - Password: X (literally the letter X)

## Running the Application

### Development Mode

Start both backend and frontend in development mode:

```bash
npm run dev
```

This will start:
- Backend server on http://localhost:5000
- Frontend React app on http://localhost:3000

### Production Mode

1. **Build the frontend:**
   ```bash
   npm run build
   ```

2. **Start the backend:**
   ```bash
   npm run start
   ```

## Using the Migration Tool

### 1. Access the Application

Open your browser and navigate to http://localhost:3000

### 2. Configure APIs

1. Go to the **Configuration** tab
2. Enter your source and target API credentials
3. Test the connections to ensure they work
4. Save the configuration

### 3. Start Migration

1. Go to the **Migration** tab
2. Review your API configuration
3. Adjust migration settings if needed:
   - **Batch Size**: Number of tickets to process at once (recommended: 50)
   - **Rate Limit**: Delay between API calls in milliseconds (recommended: 1000)
   - **Max Retries**: Number of retry attempts for failed requests (recommended: 3)
4. Click **Start Migration**

### 4. Monitor Progress

- Real-time progress updates will appear automatically
- View detailed statistics including:
  - Ticket migration progress
  - Contact migration status
  - Company migration status
  - Conversation and attachment migration
- The migration continues running in the background even if you refresh the page

### 5. View Reports

1. Go to the **Reports** tab
2. View detailed migration reports including:
   - Success/failure statistics
   - Failed ticket details with error messages
   - Contact and company migration results
   - Export reports as CSV for further analysis

## Migration Process

The tool follows this enhanced process with intelligent filtering:

1. **Pre-Migration Phase:**
   - Applies date range filters (default: Jan 1, 2020 to current date)
   - Filters by specific ticket types (CAH, PWC-SAKS Global, IPG, etc.)
   - Fetches tickets month by month to handle 300 ticket per request API limit
   - Identifies unique contacts and companies from filtered tickets
   - Migrates contacts first to maintain relationships
   - Migrates companies second

2. **Ticket Migration Phase:**
   - Processes filtered tickets in configurable batches
   - Maps migrated contact and company IDs to maintain relationships
   - Creates tickets in target system with proper associations
   - Migrates conversations and attachments for each ticket
   - Handles both public replies and private notes
   - Downloads and re-uploads file attachments

3. **Post-Migration:**
   - Generates detailed reports with filtering statistics
   - Logs all activities for comprehensive audit trail
   - Provides success/failure breakdown by ticket type and date range

## Features

### ✅ Complete Data Migration
- **Tickets**: All ticket data including custom fields
- **Contacts**: Customer contact information
- **Companies**: Organization data
- **Conversations**: All ticket conversations and replies
- **Attachments**: File attachments (downloaded and re-uploaded)

### ✅ Real-time Monitoring
- Live progress updates via WebSocket
- Detailed statistics and metrics
- Background processing continues even after page refresh

### ✅ Error Handling & Recovery
- Automatic retry mechanism for failed requests
- Detailed error logging and reporting
- Rate limiting to respect API limits
- Graceful handling of network issues

### ✅ Comprehensive Reporting
- Success/failure statistics
- Detailed error reports
- CSV export functionality
- Migration audit trail

## Troubleshooting

### Common Issues

1. **Connection Failed:**
   - Verify API URLs are correct
   - Check API keys are valid
   - Ensure API access is enabled in Freshdesk settings

2. **Rate Limiting:**
   - Increase the rate limit delay in configuration
   - Reduce batch size
   - Check your Freshdesk API rate limits

3. **Migration Stuck:**
   - Check the logs in `backend/logs/migration.log`
   - Verify network connectivity
   - Restart the migration if necessary

4. **Database Issues:**
   - The SQLite database is created automatically
   - Database file location: `backend/database/migration.db`
   - Delete the database file to reset all migration data

### Logs

Check the following log files for debugging:
- `backend/logs/migration.log` - General application logs
- `backend/logs/error.log` - Error-specific logs

### Support

For issues or questions:
1. Check the logs for detailed error messages
2. Verify your API credentials and permissions
3. Ensure your Freshdesk instances are accessible
4. Review the migration reports for specific failure details

## Security Notes

- API keys are stored securely and not logged
- All communication uses HTTPS
- Database contains migration metadata only
- No sensitive customer data is permanently stored

## Performance Tips

- Start with a small batch size (10-20) for testing
- Increase batch size gradually based on performance
- Monitor API rate limits and adjust delays accordingly
- Run migrations during off-peak hours for better performance
