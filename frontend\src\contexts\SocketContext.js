import React, { createContext, useContext, useEffect, useState } from 'react';
import io from 'socket.io-client';
import { toast } from 'react-toastify';

const SocketContext = createContext();

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [migrationProgress, setMigrationProgress] = useState({});
  const [migrationStatus, setMigrationStatus] = useState({});

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000');
    
    newSocket.on('connect', () => {
      console.log('Connected to server');
      setConnected(true);
      toast.success('Connected to migration server');
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnected(false);
      toast.warning('Disconnected from migration server');
    });

    newSocket.on('connected', (data) => {
      console.log('Server confirmation:', data);
    });

    // Migration progress updates
    newSocket.on('migrationProgress', (data) => {
      console.log('Migration progress:', data);
      setMigrationProgress(prev => ({
        ...prev,
        [data.migrationId]: data
      }));
    });

    // Migration status updates
    newSocket.on('migrationStatus', (data) => {
      console.log('Migration status:', data);
      setMigrationStatus(prev => ({
        ...prev,
        [data.migrationId]: data
      }));
      
      // Show toast notifications for status changes
      if (data.status === 'completed') {
        toast.success(`Migration ${data.migrationId} completed successfully!`);
      } else if (data.status === 'failed') {
        toast.error(`Migration ${data.migrationId} failed`);
      } else if (data.status === 'cancelled') {
        toast.info(`Migration ${data.migrationId} was cancelled`);
      }
    });

    // Ticket updates
    newSocket.on('ticketUpdate', (data) => {
      console.log('Ticket update:', data);
      // You can handle individual ticket updates here if needed
    });

    // Migration errors
    newSocket.on('migrationError', (data) => {
      console.error('Migration error:', data);
      toast.error(`Migration error: ${data.error}`);
    });

    // General notifications
    newSocket.on('notification', (data) => {
      console.log('Notification:', data);
      if (data.type === 'error') {
        toast.error(data.message);
      } else if (data.type === 'warning') {
        toast.warning(data.message);
      } else {
        toast.info(data.message);
      }
    });

    setSocket(newSocket);

    // Cleanup on unmount
    return () => {
      newSocket.close();
    };
  }, []);

  const joinMigration = (migrationId) => {
    if (socket) {
      socket.emit('joinMigration', migrationId);
      console.log(`Joined migration room: ${migrationId}`);
    }
  };

  const leaveMigration = (migrationId) => {
    if (socket) {
      socket.emit('leaveMigration', migrationId);
      console.log(`Left migration room: ${migrationId}`);
    }
  };

  const value = {
    socket,
    connected,
    migrationProgress,
    migrationStatus,
    joinMigration,
    leaveMigration
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
