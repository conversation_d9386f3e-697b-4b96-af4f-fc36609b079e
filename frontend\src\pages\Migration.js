import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Box,
  Alert,
  CircularProgress,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Schedule as PendingIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { format } from 'date-fns';

import { migrationApi, configApi, handleApiError } from '../services/api';
import { useSocket } from '../contexts/SocketContext';

const Migration = () => {
  const { migrationId } = useParams();
  const navigate = useNavigate();
  const { connected, migrationProgress, migrationStatus, joinMigration, leaveMigration } = useSocket();
  
  const [loading, setLoading] = useState(false);
  const [starting, setStarting] = useState(false);
  const [stopping, setStopping] = useState(false);
  const [migration, setMigration] = useState(null);
  const [config, setConfig] = useState({
    sourceUrl: '',
    sourceApiKey: '',
    targetUrl: '',
    targetApiKey: '',
    batchSize: 50,
    rateLimit: 1000,
    maxRetries: 3,
    dateRangeFrom: '2020-01-01',
    dateRangeTo: new Date().toISOString().split('T')[0],
    ticketTypes: [
      'CAH',
      'PWC-SAKS Global',
      'IPG',
      'Inspire Brands',
      'Costa Forms',
      'Robertshaw',
      'Albert Heijn',
      'HACH',
      'Uline',
      'Accuride'
    ]
  });
  const [confirmDialog, setConfirmDialog] = useState({ open: false, action: null });

  useEffect(() => {
    if (migrationId) {
      loadMigrationStatus();
      joinMigration(migrationId);

      return () => {
        leaveMigration(migrationId);
      };
    }
  }, [migrationId, joinMigration, leaveMigration]);

  useEffect(() => {
    // Load configuration from backend on component mount
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    try {
      const response = await configApi.get();
      if (response.data.config) {
        setConfig(prev => ({ ...prev, ...response.data.config }));
      }
    } catch (err) {
      const errorInfo = handleApiError(err);
      console.error('Failed to load configuration:', errorInfo.message);
    }
  };

  const loadMigrationStatus = async () => {
    if (!migrationId) return;
    
    try {
      setLoading(true);
      const response = await migrationApi.getStatus(migrationId);
      setMigration(response.data);
    } catch (err) {
      const errorInfo = handleApiError(err);
      toast.error(`Failed to load migration status: ${errorInfo.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field) => (event) => {
    const value = event.target.type === 'number' ? 
      parseInt(event.target.value) || 0 : 
      event.target.value;
    
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const startMigration = async () => {
    if (!config.sourceUrl || !config.sourceApiKey || !config.targetUrl || !config.targetApiKey) {
      toast.error('Please fill in all required API configuration fields');
      return;
    }

    setStarting(true);
    try {
      const response = await migrationApi.start({
        sourceUrl: config.sourceUrl,
        sourceApiKey: config.sourceApiKey,
        targetUrl: config.targetUrl,
        targetApiKey: config.targetApiKey,
        options: {
          batchSize: config.batchSize,
          rateLimit: config.rateLimit,
          maxRetries: config.maxRetries,
          dateRangeFrom: config.dateRangeFrom,
          dateRangeTo: config.dateRangeTo,
          ticketTypes: config.ticketTypes
        }
      });

      toast.success('Migration started successfully!');
      navigate(`/migration/${response.data.migrationId}`);
      
    } catch (err) {
      const errorInfo = handleApiError(err);
      toast.error(`Failed to start migration: ${errorInfo.message}`);
    } finally {
      setStarting(false);
    }
  };

  const stopMigration = async () => {
    if (!migrationId) return;
    
    setStopping(true);
    try {
      await migrationApi.stop(migrationId);
      toast.success('Migration stopped successfully');
      setConfirmDialog({ open: false, action: null });
      loadMigrationStatus();
    } catch (err) {
      const errorInfo = handleApiError(err);
      toast.error(`Failed to stop migration: ${errorInfo.message}`);
    } finally {
      setStopping(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'running': case 'processing': return 'primary';
      case 'failed': return 'error';
      case 'cancelled': return 'warning';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <SuccessIcon />;
      case 'failed': return <ErrorIcon />;
      case 'running': case 'processing': return <PendingIcon />;
      default: return <PendingIcon />;
    }
  };

  const getCurrentProgress = () => {
    if (migrationId && migrationProgress[migrationId]) {
      return migrationProgress[migrationId];
    }
    if (migration) {
      return {
        processed: migration.migration.processed_tickets || 0,
        successful: migration.migration.successful_tickets || 0,
        failed: migration.migration.failed_tickets || 0,
        total: migration.migration.total_tickets || 0,
        progress: migration.progress || 0
      };
    }
    return null;
  };

  const getCurrentStatus = () => {
    if (migrationId && migrationStatus[migrationId]) {
      return migrationStatus[migrationId].status;
    }
    return migration?.migration?.status || 'pending';
  };

  const progress = getCurrentProgress();
  const currentStatus = getCurrentStatus();
  const isRunning = currentStatus === 'running' || currentStatus === 'processing';

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {migrationId ? 'Migration Status' : 'Start New Migration'}
      </Typography>

      {!connected && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          Not connected to migration server. Real-time updates may not work.
        </Alert>
      )}

      {migrationId ? (
        // Migration Status View
        <Grid container spacing={3}>
          {/* Status Overview */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">
                    Migration: {migrationId}
                  </Typography>
                  <Box display="flex" gap={1}>
                    <Chip
                      icon={getStatusIcon(currentStatus)}
                      label={currentStatus}
                      color={getStatusColor(currentStatus)}
                    />
                    <Button
                      startIcon={<RefreshIcon />}
                      onClick={loadMigrationStatus}
                      disabled={loading}
                      size="small"
                    >
                      Refresh
                    </Button>
                    {isRunning && (
                      <Button
                        startIcon={<StopIcon />}
                        onClick={() => setConfirmDialog({ open: true, action: 'stop' })}
                        color="error"
                        variant="outlined"
                        size="small"
                      >
                        Stop Migration
                      </Button>
                    )}
                  </Box>
                </Box>

                {progress && (
                  <Box>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">
                        Progress: {progress.processed} / {progress.total} tickets
                      </Typography>
                      <Typography variant="body2">
                        {progress.progress?.toFixed(1) || 0}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={progress.progress || 0}
                      sx={{ mb: 2, height: 8, borderRadius: 4 }}
                    />
                    
                    <Grid container spacing={2}>
                      <Grid item xs={4}>
                        <Typography variant="body2" color="textSecondary">
                          Successful
                        </Typography>
                        <Typography variant="h6" color="success.main">
                          {progress.successful || 0}
                        </Typography>
                      </Grid>
                      <Grid item xs={4}>
                        <Typography variant="body2" color="textSecondary">
                          Failed
                        </Typography>
                        <Typography variant="h6" color="error.main">
                          {progress.failed || 0}
                        </Typography>
                      </Grid>
                      <Grid item xs={4}>
                        <Typography variant="body2" color="textSecondary">
                          Remaining
                        </Typography>
                        <Typography variant="h6">
                          {(progress.total || 0) - (progress.processed || 0)}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                )}

                {migration && (
                  <Box mt={2}>
                    <Typography variant="body2" color="textSecondary">
                      Created: {format(new Date(migration.migration.created_at), 'MMM dd, yyyy HH:mm:ss')}
                    </Typography>
                    {migration.migration.started_at && (
                      <Typography variant="body2" color="textSecondary">
                        Started: {format(new Date(migration.migration.started_at), 'MMM dd, yyyy HH:mm:ss')}
                      </Typography>
                    )}
                    {migration.migration.completed_at && (
                      <Typography variant="body2" color="textSecondary">
                        Completed: {format(new Date(migration.migration.completed_at), 'MMM dd, yyyy HH:mm:ss')}
                      </Typography>
                    )}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Migration Details */}
          {migration && (
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Migration Details
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="textSecondary">
                        Source URL
                      </Typography>
                      <Typography variant="body1">
                        {migration.migration.source_url}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="textSecondary">
                        Target URL
                      </Typography>
                      <Typography variant="body1">
                        {migration.migration.target_url}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      ) : (
        // Start New Migration Form
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary">
                  Source Configuration
                </Typography>
                
                <TextField
                  fullWidth
                  label="Source API URL"
                  value={config.sourceUrl}
                  onChange={handleInputChange('sourceUrl')}
                  margin="normal"
                  required
                />
                
                <TextField
                  fullWidth
                  label="Source API Key"
                  type="password"
                  value={config.sourceApiKey}
                  onChange={handleInputChange('sourceApiKey')}
                  margin="normal"
                  required
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="secondary">
                  Target Configuration
                </Typography>
                
                <TextField
                  fullWidth
                  label="Target API URL"
                  value={config.targetUrl}
                  onChange={handleInputChange('targetUrl')}
                  margin="normal"
                  required
                />
                
                <TextField
                  fullWidth
                  label="Target API Key"
                  type="password"
                  value={config.targetApiKey}
                  onChange={handleInputChange('targetApiKey')}
                  margin="normal"
                  required
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Migration Options
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="Batch Size"
                      type="number"
                      value={config.batchSize}
                      onChange={handleInputChange('batchSize')}
                      margin="normal"
                      inputProps={{ min: 1, max: 100 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="Rate Limit (ms)"
                      type="number"
                      value={config.rateLimit}
                      onChange={handleInputChange('rateLimit')}
                      margin="normal"
                      inputProps={{ min: 100, max: 10000 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      label="Max Retries"
                      type="number"
                      value={config.maxRetries}
                      onChange={handleInputChange('maxRetries')}
                      margin="normal"
                      inputProps={{ min: 0, max: 10 }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Migration Filters Summary */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Migration Filters
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="textSecondary">
                      Date Range
                    </Typography>
                    <Typography variant="body1">
                      {config.dateRangeFrom} to {config.dateRangeTo}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" color="textSecondary">
                      Ticket Types ({config.ticketTypes.length} selected)
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                      {config.ticketTypes.slice(0, 5).map((type) => (
                        <Chip key={type} label={type} size="small" />
                      ))}
                      {config.ticketTypes.length > 5 && (
                        <Chip
                          label={`+${config.ticketTypes.length - 5} more`}
                          size="small"
                          variant="outlined"
                        />
                      )}
                    </Box>
                  </Grid>
                </Grid>

                <Alert severity="info" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    Migration will process tickets month by month to handle API limits.
                    Only tickets matching the selected types and date range will be migrated.
                  </Typography>
                </Alert>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Box display="flex" justifyContent="center">
              <Button
                variant="contained"
                size="large"
                startIcon={starting ? <CircularProgress size={16} /> : <StartIcon />}
                onClick={startMigration}
                disabled={starting}
                sx={{ minWidth: 200 }}
              >
                {starting ? 'Starting Migration...' : 'Start Migration'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      )}

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialog.open}
        onClose={() => setConfirmDialog({ open: false, action: null })}
      >
        <DialogTitle>Confirm Action</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to stop this migration? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialog({ open: false, action: null })}>
            Cancel
          </Button>
          <Button
            onClick={stopMigration}
            color="error"
            disabled={stopping}
            startIcon={stopping ? <CircularProgress size={16} /> : <StopIcon />}
          >
            {stopping ? 'Stopping...' : 'Stop Migration'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Migration;
