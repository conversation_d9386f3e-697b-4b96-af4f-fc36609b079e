const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');

const dbDir = path.join(__dirname, '../database');
const dbPath = process.env.DB_PATH || path.join(dbDir, 'migration.db');

// Ensure database directory exists
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

let db = null;

const initialize = async () => {
  return new Promise((resolve, reject) => {
    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        logger.error('Error opening database:', err);
        reject(err);
        return;
      }
      
      logger.info('Connected to SQLite database');
      createTables()
        .then(() => resolve())
        .catch(reject);
    });
  });
};

const createTables = async () => {
  const tables = [
    // Migration sessions table
    `CREATE TABLE IF NOT EXISTS migrations (
      id TEXT PRIMARY KEY,
      source_url TEXT NOT NULL,
      source_api_key TEXT NOT NULL,
      target_url TEXT NOT NULL,
      target_api_key TEXT NOT NULL,
      status TEXT DEFAULT 'pending',
      total_tickets INTEGER DEFAULT 0,
      processed_tickets INTEGER DEFAULT 0,
      successful_tickets INTEGER DEFAULT 0,
      failed_tickets INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      started_at DATETIME,
      completed_at DATETIME,
      error_message TEXT
    )`,
    
    // Individual ticket migration records
    `CREATE TABLE IF NOT EXISTS ticket_migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      migration_id TEXT NOT NULL,
      source_ticket_id TEXT NOT NULL,
      target_ticket_id TEXT,
      status TEXT DEFAULT 'pending',
      error_message TEXT,
      retry_count INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      migrated_at DATETIME,
      FOREIGN KEY (migration_id) REFERENCES migrations (id)
    )`,
    
    // Conversation migration records
    `CREATE TABLE IF NOT EXISTS conversation_migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      ticket_migration_id INTEGER NOT NULL,
      source_conversation_id TEXT NOT NULL,
      target_conversation_id TEXT,
      status TEXT DEFAULT 'pending',
      error_message TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      migrated_at DATETIME,
      FOREIGN KEY (ticket_migration_id) REFERENCES ticket_migrations (id)
    )`,
    
    // Attachment migration records
    `CREATE TABLE IF NOT EXISTS attachment_migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      conversation_migration_id INTEGER NOT NULL,
      source_attachment_id TEXT NOT NULL,
      target_attachment_id TEXT,
      filename TEXT,
      status TEXT DEFAULT 'pending',
      error_message TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      migrated_at DATETIME,
      FOREIGN KEY (conversation_migration_id) REFERENCES conversation_migrations (id)
    )`,

    // Contact migration records
    `CREATE TABLE IF NOT EXISTS contact_migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      migration_id TEXT NOT NULL,
      source_contact_id TEXT NOT NULL,
      target_contact_id TEXT,
      email TEXT,
      name TEXT,
      status TEXT DEFAULT 'pending',
      error_message TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      migrated_at DATETIME,
      FOREIGN KEY (migration_id) REFERENCES migrations (id)
    )`,

    // Company migration records
    `CREATE TABLE IF NOT EXISTS company_migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      migration_id TEXT NOT NULL,
      source_company_id TEXT NOT NULL,
      target_company_id TEXT,
      name TEXT,
      domain TEXT,
      status TEXT DEFAULT 'pending',
      error_message TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      migrated_at DATETIME,
      FOREIGN KEY (migration_id) REFERENCES migrations (id)
    )`,
    
    // Configuration table
    `CREATE TABLE IF NOT EXISTS configurations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      key TEXT UNIQUE NOT NULL,
      value TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`
  ];

  for (const tableSQL of tables) {
    await new Promise((resolve, reject) => {
      db.run(tableSQL, (err) => {
        if (err) {
          logger.error('Error creating table:', err);
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }
  
  logger.info('Database tables created successfully');
};

const getDatabase = () => {
  if (!db) {
    throw new Error('Database not initialized');
  }
  return db;
};

const close = () => {
  if (db) {
    db.close((err) => {
      if (err) {
        logger.error('Error closing database:', err);
      } else {
        logger.info('Database connection closed');
      }
    });
  }
};

module.exports = {
  initialize,
  getDatabase,
  close
};
