{"name": "ticket-migration-tool", "version": "1.0.0", "description": "A comprehensive ticket migration tool for transferring tickets between support systems", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd backend && npm run dev", "client:dev": "cd frontend && npm start", "server:start": "cd backend && npm start", "client:build": "cd frontend && npm run build", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "npm run client:build", "start": "npm run server:start", "test:migration": "node test-migration.js"}, "keywords": ["ticket", "migration", "freshdesk", "support", "api"], "author": "Ticket Mi<PERSON>l", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}