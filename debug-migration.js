const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Connect to the migration database
const dbPath = path.join(__dirname, 'backend', 'database', 'migration.db');
const db = new sqlite3.Database(dbPath);

console.log('🔍 Debugging Migration Database...\n');

// Check migrations table
db.all("SELECT * FROM migrations ORDER BY created_at DESC LIMIT 5", (err, rows) => {
  if (err) {
    console.error('Error reading migrations:', err);
    return;
  }
  
  console.log('📊 Recent Migrations:');
  console.table(rows);
  
  if (rows.length > 0) {
    const latestMigration = rows[0];
    console.log(`\n🎯 Latest Migration Details:`);
    console.log(`ID: ${latestMigration.id}`);
    console.log(`Status: ${latestMigration.status}`);
    console.log(`Total Tickets: ${latestMigration.total_tickets}`);
    console.log(`Processed: ${latestMigration.processed_tickets}`);
    console.log(`Successful: ${latestMigration.successful_tickets}`);
    console.log(`Failed: ${latestMigration.failed_tickets}`);
    console.log(`Error: ${latestMigration.error_message || 'None'}`);
    
    // Check ticket migrations for this migration
    db.all("SELECT * FROM ticket_migrations WHERE migration_id = ? LIMIT 10", [latestMigration.id], (err, ticketRows) => {
      if (err) {
        console.error('Error reading ticket migrations:', err);
        return;
      }
      
      console.log(`\n🎫 Ticket Migration Details (showing first 10):`);
      console.table(ticketRows);
      
      // Check for failed tickets
      db.all("SELECT * FROM ticket_migrations WHERE migration_id = ? AND status = 'failed'", [latestMigration.id], (err, failedRows) => {
        if (err) {
          console.error('Error reading failed tickets:', err);
          return;
        }
        
        if (failedRows.length > 0) {
          console.log(`\n❌ Failed Tickets (${failedRows.length}):`);
          failedRows.forEach(ticket => {
            console.log(`- Ticket ${ticket.source_ticket_id}: ${ticket.error_message}`);
          });
        }
        
        db.close();
      });
    });
  } else {
    console.log('No migrations found in database.');
    db.close();
  }
});
