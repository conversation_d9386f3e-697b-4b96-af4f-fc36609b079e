const request = require('supertest');
const path = require('path');

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DB_PATH = ':memory:'; // Use in-memory database for tests
process.env.PORT = 5001;

const app = require('../server');

describe('Migration API', () => {
  beforeAll(async () => {
    // Wait for server to initialize
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  describe('Health Check', () => {
    test('GET /api/health should return OK', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body.status).toBe('OK');
      expect(response.body.version).toBe('1.0.0');
    });
  });

  describe('Configuration API', () => {
    test('GET /api/config should return configuration', async () => {
      const response = await request(app)
        .get('/api/config')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.config).toBeDefined();
    });

    test('POST /api/config should update configuration', async () => {
      const config = {
        sourceUrl: 'https://test.freshdesk.com/api/v2',
        targetUrl: 'https://test-target.freshdesk.com/api/v2',
        batchSize: 25
      };

      const response = await request(app)
        .post('/api/config')
        .send({ config })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('updated successfully');
    });

    test('POST /api/config/test-connection should validate connection data', async () => {
      const connectionData = {
        apiUrl: 'https://invalid-url',
        apiKey: 'invalid-key'
      };

      const response = await request(app)
        .post('/api/config/test-connection')
        .send(connectionData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    test('GET /api/config/api-info/freshdesk should return API information', async () => {
      const response = await request(app)
        .get('/api/config/api-info/freshdesk')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.apiInfo.name).toBe('Freshdesk');
    });
  });

  describe('Migration API', () => {
    test('POST /api/migration/start should require all fields', async () => {
      const response = await request(app)
        .post('/api/migration/start')
        .send({})
        .expect(400);

      expect(response.body.error).toContain('Missing required fields');
    });

    test('POST /api/migration/start should create migration with valid data', async () => {
      const migrationData = {
        sourceUrl: 'https://test.freshdesk.com/api/v2',
        sourceApiKey: 'test-key',
        targetUrl: 'https://test-target.freshdesk.com/api/v2',
        targetApiKey: 'test-target-key'
      };

      const response = await request(app)
        .post('/api/migration/start')
        .send(migrationData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.migrationId).toBeDefined();
    });

    test('GET /api/migration/list should return migrations', async () => {
      const response = await request(app)
        .get('/api/migration/list')
        .expect(200);

      expect(response.body.migrations).toBeDefined();
      expect(response.body.pagination).toBeDefined();
    });
  });

  describe('Reports API', () => {
    test('GET /api/reports should return reports summary', async () => {
      const response = await request(app)
        .get('/api/reports')
        .expect(200);

      expect(response.body.migrations).toBeDefined();
      expect(response.body.overallStats).toBeDefined();
    });

    test('GET /api/reports/invalid-id should return 404', async () => {
      const response = await request(app)
        .get('/api/reports/invalid-migration-id')
        .expect(404);

      expect(response.body.error).toContain('Migration not found');
    });
  });
});

describe('Migration Service Unit Tests', () => {
  const MigrationService = require('../services/migrationService');

  test('should create MigrationService instance', () => {
    const config = {
      migrationId: 'test-id',
      sourceUrl: 'https://test.freshdesk.com/api/v2',
      sourceApiKey: 'test-key',
      targetUrl: 'https://test-target.freshdesk.com/api/v2',
      targetApiKey: 'test-target-key'
    };

    const service = new MigrationService(config);
    expect(service.migrationId).toBe('test-id');
    expect(service.batchSize).toBe(50); // default value
  });

  test('should transform contact data correctly', () => {
    const config = {
      migrationId: 'test-id',
      sourceUrl: 'https://test.freshdesk.com/api/v2',
      sourceApiKey: 'test-key',
      targetUrl: 'https://test-target.freshdesk.com/api/v2',
      targetApiKey: 'test-target-key'
    };

    const service = new MigrationService(config);
    
    const sourceContact = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '************',
      custom_fields: { department: 'IT' }
    };

    const transformed = service.transformContactData(sourceContact);
    
    expect(transformed.name).toBe('John Doe');
    expect(transformed.email).toBe('<EMAIL>');
    expect(transformed.phone).toBe('************');
    expect(transformed.custom_fields.department).toBe('IT');
  });

  test('should transform company data correctly', () => {
    const config = {
      migrationId: 'test-id',
      sourceUrl: 'https://test.freshdesk.com/api/v2',
      sourceApiKey: 'test-key',
      targetUrl: 'https://test-target.freshdesk.com/api/v2',
      targetApiKey: 'test-target-key'
    };

    const service = new MigrationService(config);
    
    const sourceCompany = {
      name: 'Acme Corp',
      description: 'Technology company',
      domains: ['acme.com'],
      custom_fields: { industry: 'Technology' }
    };

    const transformed = service.transformCompanyData(sourceCompany);
    
    expect(transformed.name).toBe('Acme Corp');
    expect(transformed.description).toBe('Technology company');
    expect(transformed.domains).toEqual(['acme.com']);
    expect(transformed.custom_fields.industry).toBe('Technology');
  });
});

describe('Database Operations', () => {
  const database = require('../database/init');

  test('should initialize database successfully', async () => {
    await expect(database.initialize()).resolves.not.toThrow();
  });

  test('should get database instance', () => {
    const db = database.getDatabase();
    expect(db).toBeDefined();
  });
});

describe('Socket Service', () => {
  const socketService = require('../services/socketService');

  test('should have required methods', () => {
    expect(typeof socketService.initializeSocket).toBe('function');
    expect(typeof socketService.emitMigrationProgress).toBe('function');
    expect(typeof socketService.emitMigrationStatus).toBe('function');
    expect(typeof socketService.emitTicketUpdate).toBe('function');
  });
});

describe('Logger', () => {
  const logger = require('../utils/logger');

  test('should have logging methods', () => {
    expect(typeof logger.info).toBe('function');
    expect(typeof logger.error).toBe('function');
    expect(typeof logger.warn).toBe('function');
    expect(typeof logger.debug).toBe('function');
  });

  test('should have migration-specific logging methods', () => {
    expect(typeof logger.logMigrationStart).toBe('function');
    expect(typeof logger.logMigrationComplete).toBe('function');
    expect(typeof logger.logTicketMigration).toBe('function');
  });
});
