const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Connect to the database
const dbPath = path.join(__dirname, 'backend', 'database', 'migration.db');
const db = new sqlite3.Database(dbPath);

console.log('🗑️ Clearing stored configuration from database...');

// Clear all configuration entries
db.run('DELETE FROM configurations WHERE key IN (?, ?, ?, ?)', 
  ['sourceUrl', 'sourceApiKey', 'targetUrl', 'targetApiKey'], 
  function(err) {
    if (err) {
      console.error('❌ Error clearing configuration:', err);
    } else {
      console.log(`✅ Cleared ${this.changes} configuration entries from database`);
      console.log('🔄 Environment variables will now be used as defaults');
    }
    
    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err);
      } else {
        console.log('✅ Database connection closed');
      }
    });
  }
);
