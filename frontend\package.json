{"name": "ticket-migration-frontend", "version": "1.0.0", "description": "React frontend for ticket migration tool", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.15.0", "socket.io-client": "^4.7.2", "axios": "^1.5.0", "recharts": "^2.8.0", "react-toastify": "^9.1.3", "styled-components": "^6.0.7", "@mui/material": "^5.14.5", "@mui/icons-material": "^5.14.3", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "date-fns": "^2.30.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}