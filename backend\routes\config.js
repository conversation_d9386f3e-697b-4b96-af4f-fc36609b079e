const express = require('express');
const logger = require('../utils/logger');
const { getDatabase } = require('../database/init');

const router = express.Router();

// Get configuration
router.get('/', async (req, res) => {
  try {
    const db = getDatabase();

    const configs = await new Promise((resolve, reject) => {
      db.all(
        'SELECT key, value FROM configurations',
        (err, rows) => {
          if (err) reject(err);
          else {
            const configObj = {};
            rows.forEach(row => {
              try {
                configObj[row.key] = JSON.parse(row.value);
              } catch {
                configObj[row.key] = row.value;
              }
            });
            resolve(configObj);
          }
        }
      );
    });

    // Merge with environment variable defaults
    const defaultConfig = {
      sourceUrl: process.env.SOURCE_API_URL || '',
      sourceApiKey: process.env.SOURCE_API_KEY || '',
      targetUrl: process.env.TARGET_API_URL || '',
      targetApiKey: process.env.TARGET_API_KEY || '',
      batchSize: parseInt(process.env.BATCH_SIZE) || 50,
      rateLimit: parseInt(process.env.RATE_LIMIT_DELAY_MS) || 1000,
      maxRetries: parseInt(process.env.MAX_RETRIES) || 3
    };

    // Merge database config with defaults (database values take precedence)
    const finalConfig = { ...defaultConfig, ...configs };

    res.json({
      success: true,
      config: finalConfig
    });

  } catch (error) {
    logger.error('Error getting configuration:', error);
    res.status(500).json({
      error: 'Failed to get configuration',
      message: error.message
    });
  }
});

// Update configuration
router.post('/', async (req, res) => {
  try {
    const { config } = req.body;
    
    if (!config || typeof config !== 'object') {
      return res.status(400).json({
        error: 'Invalid configuration data'
      });
    }

    const db = getDatabase();
    
    // Update each configuration key
    for (const [key, value] of Object.entries(config)) {
      const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value);
      
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT OR REPLACE INTO configurations (key, value, updated_at)
           VALUES (?, ?, CURRENT_TIMESTAMP)`,
          [key, valueStr],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    }

    logger.info('Configuration updated', { keys: Object.keys(config) });

    res.json({
      success: true,
      message: 'Configuration updated successfully'
    });

  } catch (error) {
    logger.error('Error updating configuration:', error);
    res.status(500).json({
      error: 'Failed to update configuration',
      message: error.message
    });
  }
});

// Test API connection
router.post('/test-connection', async (req, res) => {
  try {
    const { apiUrl, apiKey, type = 'freshdesk' } = req.body;
    
    if (!apiUrl || !apiKey) {
      return res.status(400).json({
        error: 'API URL and API key are required'
      });
    }

    const axios = require('axios');
    
    // Create axios instance for testing
    const api = axios.create({
      baseURL: apiUrl,
      auth: {
        username: apiKey,
        password: 'X'
      },
      timeout: 10000
    });

    // Test connection with a simple API call
    let testEndpoint = '/tickets';
    if (type === 'freshdesk') {
      testEndpoint = '/tickets?per_page=1';
    }

    const response = await api.get(testEndpoint);
    
    res.json({
      success: true,
      message: 'Connection successful',
      status: response.status,
      responseTime: response.headers['x-response-time'] || 'N/A'
    });

  } catch (error) {
    logger.error('API connection test failed:', error);
    
    let errorMessage = 'Connection failed';
    if (error.response) {
      errorMessage = `HTTP ${error.response.status}: ${error.response.statusText}`;
    } else if (error.code === 'ENOTFOUND') {
      errorMessage = 'Invalid URL or network error';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'Connection refused';
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = 'Connection timeout';
    }

    res.status(400).json({
      success: false,
      error: errorMessage,
      details: error.message
    });
  }
});

// Get API information
router.get('/api-info/:type', async (req, res) => {
  try {
    const { type } = req.params;
    
    const apiInfo = {
      freshdesk: {
        name: 'Freshdesk',
        baseUrl: 'https://your-domain.freshdesk.com/api/v2',
        authType: 'basic',
        documentation: 'https://developers.freshdesk.com/api/',
        endpoints: {
          tickets: '/tickets',
          conversations: '/tickets/{id}/conversations',
          attachments: '/attachments/{id}'
        },
        rateLimit: '1000 requests per hour',
        notes: [
          'Use your API key as username and "X" as password',
          'Replace "your-domain" with your actual Freshdesk domain',
          'Ensure API access is enabled in your Freshdesk settings'
        ]
      }
    };

    const info = apiInfo[type];
    if (!info) {
      return res.status(404).json({
        error: 'API type not supported',
        supportedTypes: Object.keys(apiInfo)
      });
    }

    res.json({
      success: true,
      apiInfo: info
    });

  } catch (error) {
    logger.error('Error getting API info:', error);
    res.status(500).json({
      error: 'Failed to get API information',
      message: error.message
    });
  }
});

module.exports = router;
