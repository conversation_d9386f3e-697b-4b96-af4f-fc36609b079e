const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Helper function to safely stringify objects with circular references
const safeStringify = (obj) => {
  const seen = new WeakSet();
  return JSON.stringify(obj, (key, val) => {
    if (val != null && typeof val === 'object') {
      if (seen.has(val)) {
        return '[Circular]';
      }
      seen.add(val);
    }
    return val;
  });
};

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let metaStr = '';
    if (Object.keys(meta).length > 0) {
      try {
        metaStr = ' ' + safeStringify(meta);
      } catch (error) {
        metaStr = ' [Error serializing metadata]';
      }
    }
    return `${timestamp} [${level}]: ${message}${metaStr}`;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const logEntry = {
      timestamp,
      level,
      message,
      ...meta
    };
    try {
      return safeStringify(logEntry);
    } catch (error) {
      return safeStringify({
        timestamp,
        level,
        message,
        error: 'Failed to serialize log entry'
      });
    }
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: fileFormat,
  defaultMeta: { service: 'ticket-migration' },
  transports: [
    // File transport for all logs
    new winston.transports.File({
      filename: path.join(logsDir, 'migration.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      tailable: true
    }),
    // File transport for errors only
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      tailable: true
    })
  ]
});

// Add console transport in development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

// Helper methods for structured logging
logger.logMigrationStart = (migrationId, config) => {
  logger.info('Migration started', {
    migrationId,
    sourceUrl: config.sourceUrl,
    targetUrl: config.targetUrl,
    timestamp: new Date().toISOString()
  });
};

logger.logMigrationComplete = (migrationId, stats) => {
  logger.info('Migration completed', {
    migrationId,
    ...stats,
    timestamp: new Date().toISOString()
  });
};

logger.logTicketMigration = (migrationId, ticketId, status, error = null) => {
  const logData = {
    migrationId,
    ticketId,
    status,
    timestamp: new Date().toISOString()
  };
  
  if (error) {
    logData.error = error.message;
    logData.stack = error.stack;
  }
  
  if (status === 'success') {
    logger.info('Ticket migrated successfully', logData);
  } else {
    logger.error('Ticket migration failed', logData);
  }
};

module.exports = logger;
