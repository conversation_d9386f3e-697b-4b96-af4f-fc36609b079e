const axios = require('axios');
const FormData = require('form-data');

// Test configuration
const testConfig = {
  sourceUrl: process.env.SOURCE_FRESHDESK_URL || 'https://pandohelp.freshdesk.com/api/v2',
  sourceApiKey: process.env.SOURCE_API_KEY || 'your-source-api-key',
  targetUrl: process.env.TARGET_FRESHDESK_URL || 'https://pandohelp.freshdesk.com/api/v2',
  targetApiKey: process.env.TARGET_API_KEY || 'your-target-api-key'
};

async function testTicketCreationWithFormData() {
  console.log('🎫 Testing Ticket Creation with Form Data');
  console.log('==========================================');

  try {
    const form = new FormData();
    
    // Add ticket data using form format as per your curl example
    form.append('email', '<EMAIL>');
    form.append('subject', 'Test Ticket from Migration Tool');
    form.append('description', 'This is a test ticket created using form data format');
    form.append('type', 'JNJ');
    form.append('priority', '1');
    form.append('status', '2');

    console.log('📤 Sending ticket creation request...');
    console.log('Form data fields:', {
      email: '<EMAIL>',
      subject: 'Test Ticket from Migration Tool',
      type: 'JNJ',
      priority: '1',
      status: '2'
    });

    const response = await axios.post(
      `${testConfig.targetUrl}/tickets`,
      form,
      {
        headers: {
          ...form.getHeaders(),
          'Authorization': `Basic ${Buffer.from(`${testConfig.targetApiKey}:X`).toString('base64')}`
        },
        timeout: 60000
      }
    );

    console.log('✅ Ticket created successfully!');
    console.log('Response:', {
      id: response.data.id,
      subject: response.data.subject,
      status: response.data.status,
      priority: response.data.priority,
      type: response.data.type,
      requester_id: response.data.requester_id
    });

    return response.data;

  } catch (error) {
    console.error('❌ Error creating ticket:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    throw error;
  }
}

async function testConversationReplyWithFormData(ticketId) {
  console.log('\n💬 Testing Conversation Reply with Form Data');
  console.log('=============================================');

  try {
    const form = new FormData();
    
    // Add reply data using form format as per your curl example
    form.append('body', 'This is a test reply added using form data format from the migration tool');

    console.log('📤 Sending conversation reply request...');
    console.log('Form data fields:', {
      body: 'Test reply from migration tool',
      ticketId: ticketId
    });

    const response = await axios.post(
      `${testConfig.targetUrl}/tickets/${ticketId}/reply`,
      form,
      {
        headers: {
          ...form.getHeaders(),
          'Authorization': `Basic ${Buffer.from(`${testConfig.targetApiKey}:X`).toString('base64')}`
        },
        timeout: 30000
      }
    );

    console.log('✅ Reply added successfully!');
    console.log('Response:', {
      id: response.data.id,
      body_text: response.data.body_text?.substring(0, 100) + '...',
      user_id: response.data.user_id,
      private: response.data.private
    });

    return response.data;

  } catch (error) {
    console.error('❌ Error adding reply:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    throw error;
  }
}

async function testFilterAPI() {
  console.log('\n🔍 Testing Filter API');
  console.log('=====================');

  try {
    const query = 'priority:1';
    const params = {
      query: `"${query}"`,
      page: 1
    };

    console.log('📤 Sending filter API request...');
    console.log('Query:', query);

    const response = await axios.get(`${testConfig.sourceUrl}/search/tickets`, {
      params,
      headers: {
        'Authorization': `Basic ${Buffer.from(`${testConfig.sourceApiKey}:X`).toString('base64')}`
      },
      timeout: 30000
    });

    console.log('✅ Filter API response received!');
    console.log('Response:', {
      total: response.data.total,
      results_count: response.data.results?.length || 0,
      sample_result: response.data.results?.[0] ? {
        id: response.data.results[0].id,
        subject: response.data.results[0].subject?.substring(0, 50) + '...',
        status: response.data.results[0].status,
        type: response.data.results[0].type
      } : 'No results'
    });

    return response.data;

  } catch (error) {
    console.error('❌ Error with filter API:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    throw error;
  }
}

async function main() {
  console.log('🧪 Form Data API Testing Suite');
  console.log('===============================\n');

  // Check if credentials are provided
  if (!process.env.TARGET_API_KEY || !process.env.SOURCE_API_KEY) {
    console.log('⚠️  API credentials not provided. Set environment variables:');
    console.log('- SOURCE_FRESHDESK_URL (optional, defaults to pandohelp)');
    console.log('- SOURCE_API_KEY');
    console.log('- TARGET_FRESHDESK_URL (optional, defaults to pandohelp)');
    console.log('- TARGET_API_KEY\n');
    console.log('Example:');
    console.log('set SOURCE_API_KEY=your_source_api_key');
    console.log('set TARGET_API_KEY=your_target_api_key');
    console.log('node test-form-api.js\n');
    return;
  }

  try {
    // Test 1: Filter API
    console.log('🔍 Step 1: Testing Filter API...');
    await testFilterAPI();

    // Test 2: Ticket Creation with Form Data
    console.log('\n🎫 Step 2: Testing Ticket Creation...');
    const createdTicket = await testTicketCreationWithFormData();

    // Test 3: Conversation Reply with Form Data
    if (createdTicket && createdTicket.id) {
      console.log('\n💬 Step 3: Testing Conversation Reply...');
      await testConversationReplyWithFormData(createdTicket.id);
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('✅ Filter API working');
    console.log('✅ Form-based ticket creation working');
    console.log('✅ Form-based conversation reply working');

  } catch (error) {
    console.error('\n❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
main().catch(console.error);
